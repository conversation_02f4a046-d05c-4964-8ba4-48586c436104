import logging
import os
from dotenv import load_dotenv, set_key
from django.conf import settings

logger = logging.getLogger("app")


def save_smtp_settings(host, port, username, password, use_tls):
    # Load the environment variables from the .env file
    set_key(".env", "EMAIL_HOST", host)
    set_key(".env", "EMAIL_PORT", str(port))
    set_key(".env", "EMAIL_HOST_USER", username)
    set_key(".env", "EMAIL_HOST_PASSWORD", password)
    set_key(".env", "EMAIL_USE_TLS", str(use_tls))

    # manually updated settings
    settings.EMAIL_HOST = host
    settings.EMAIL_PORT = port
    settings.EMAIL_HOST_USER = username
    settings.EMAIL_HOST_PASSWORD = password
    settings.EMAIL_USE_TLS = use_tls


def load_smtp_settings():
    load_dotenv()  # Load the environment variables from .env file
    return {
        "host": os.getenv("EMAIL_HOST", settings.EMAIL_HOST),
        "port": int(os.getenv("EMAIL_PORT", settings.EMAIL_PORT)),
        "usrn": os.getenv("EMAIL_HOST_USER", settings.EMAIL_HOST_USER),
        "pwrd": os.getenv("EMAIL_HOST_PASSWORD", settings.EMAIL_HOST_PASSWORD),
        "tssl": os.getenv("EMAIL_USE_TLS", settings.EMAIL_USE_TLS) == "True",
    }


def save_sms_settings(sid, token, phone):
    # Load the environment variables from the .env file
    set_key(".env", "TWILIO_ACCOUNT_SID", sid)
    set_key(".env", "TWILIO_AUTH_TOKEN", token)
    set_key(".env", "TWILIO_PHONE_NUMBER", phone)

    # manually updated settings
    settings.TWILIO_ACCOUNT_SID = sid
    settings.TWILIO_AUTH_TOKEN = token
    settings.TWILIO_PHONE_NUMBER = phone


def load_sms_settings():
    load_dotenv()
    return {
        "asid": os.getenv("TWILIO_ACCOUNT_SID", settings.TWILIO_ACCOUNT_SID),
        "tokn": os.getenv("TWILIO_AUTH_TOKEN", settings.TWILIO_AUTH_TOKEN),
        "phon": os.getenv("TWILIO_PHONE_NUMBER", settings.TWILIO_PHONE_NUMBER),
    }
