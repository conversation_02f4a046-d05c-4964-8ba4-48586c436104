from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse_lazy
from django.views.generic import View, FormView
from django.contrib import messages
from scripts.communication import send_invitation

from settings.utils import (
    load_sms_settings,
    load_smtp_settings,
    save_sms_settings,
    save_smtp_settings,
)
from django.core.mail import get_connection
from .forms import SMTPSettingsForm, SMSSettingsForm, TTNSettingsForm
from django.contrib.auth.models import User
from django.contrib.auth.decorators import user_passes_test
from django.utils.decorators import method_decorator
from twilio.rest import Client


@method_decorator(user_passes_test(lambda u: u.is_superuser), name="dispatch")
class SettingsView(View):
    template_name = "settings/settings.html"
    error = "Something went wrong. Please try again later."

    def get(self, request):
        email_form = SMTPSettingsForm(initial=self.get_email_initial())
        sms_form = SMSSettingsForm(initial=self.get_sms_initial())
        return render(
            request,
            self.template_name,
            {"email_form": email_form, "sms_form": sms_form},
        )

    def post(self, request):
        if "email_form" in self.request.POST:
            form = SMTPSettingsForm(request.POST)
            self.request.session["temp_email_fields"] = [
                form["host"].value(),
                form["port"].value(),
                form["usrn"].value(),
                form["pwrd"].value(),
                form["tssl"].value(),
            ]  # Store temp_fields in session

            if self.save_email_settings(form):
                messages.success(request, "Email settings saved successfully")
            else:
                messages.error(request, "Error: " + self.error)
        elif "sms_form" in self.request.POST:
            form = SMSSettingsForm(request.POST)
            self.request.session["temp_sms_fields"] = [
                form["asid"].value(),
                form["tokn"].value(),
                form["phon"].value(),
            ]

            if self.save_sms_settings(form):
                messages.success(request, "SMS settings saved successfully")
            else:
                messages.error(request, "Error: " + self.error)
        elif "ttn_form" in self.request.POST:
            form = TTNSettingsForm(request.POST)
            self.request.session["temp_ttn_fields"] = [
                form["ttn_address"].value(),
                form["api_key"].value(),
            ]

            if self.save_ttn_settings(form):
                messages.success(request, "TTN settings saved successfully")
            else:
                messages.error(request, "Error: " + self.error)

            return redirect(reverse_lazy("settings:ttn"))

        return render(
            request,
            self.template_name,
            {
                "email_form": SMTPSettingsForm(initial=self.get_email_initial()),
                "sms_form": SMSSettingsForm(initial=self.get_sms_initial()),
            },
        )

    def save_email_settings(self, form):
        if self.test_email_config(form):
            save_smtp_settings(
                form["host"].value(),
                form["port"].value(),
                form["usrn"].value(),
                form["pwrd"].value(),
                form["tssl"].value(),
            )
            return True
        else:
            return False

    def save_sms_settings(self, form):
        if self.test_sms_config(form):
            save_sms_settings(
                form["asid"].value(),
                form["tokn"].value(),
                form["phon"].value(),
            )
            return True
        else:
            return False

    def save_ttn_settings(self, form):
        pass

    def test_email_config(self, form):
        connection = get_connection(
            host=form["host"].value(),
            port=form["port"].value(),
            username=form["usrn"].value(),
            password=form["pwrd"].value(),
            use_tls=form["tssl"].value(),
        )
        try:
            connection.open()
        except Exception as e:
            self.error = str(e)
            return False

        connection.close()
        return True

    def test_sms_config(self, form):
        try:
            client = Client(form["asid"].value(), form["tokn"].value())
            # Fetch and print the account information (just to test the connection)
            client.api.accounts(form["asid"].value()).fetch()
        except Exception as e:
            self.error = str(e)
            return False

        return True

    def test_ttn_config(self, form):
        try:
            pass
        except Exception as e:
            self.error = str(e)
            return False

    def get_email_initial(self):
        init_email = load_smtp_settings()
        temp_fields = self.request.session.pop("temp_email_fields", None)

        if temp_fields:
            # Check if any of the temp fields are None
            # If not, substitute
            for index, i in enumerate(temp_fields):
                key = list(init_email.keys())[index]
                value = init_email[key]
                if value is not None:
                    init_email[key] = i

        return init_email

    def get_sms_initial(self):
        init_sms = load_sms_settings()
        temp_fields = self.request.session.pop("temp_sms_fields", None)

        if temp_fields:
            # Check if any of the temp fields are None
            # If not, substitute
            for index, i in enumerate(temp_fields):
                key = list(init_sms.keys())[index]
                value = init_sms[key]
                if value is not None:
                    init_sms[key] = i

        return init_sms


def test_email(request):
    user = get_object_or_404(User, id=6)
    send_invitation(user)
