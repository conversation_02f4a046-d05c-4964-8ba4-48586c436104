from django import forms

from django.core.validators import EmailValidator
from django.core.exceptions import ValidationError

import socket

import phonenumbers


class SMTPSettingsForm(forms.Form):
    host = forms.CharField(
        label="Host",
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "maxlength": "254",
            }
        ),
    )
    port = forms.IntegerField(
        label="Port",
        widget=forms.NumberInput(
            attrs={
                "class": "form-control",
            }
        ),
    )
    usrn = forms.EmailField(
        label="Username",
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "maxlength": "254",
            }
        ),
    )
    pwrd = forms.CharField(
        label="Password",
        widget=forms.PasswordInput(
            attrs={
                "class": "form-control",
                "maxlength": "254",
            }
        ),
    )
    tssl = forms.BooleanField(
        label="Use SSL",
        widget=forms.CheckboxInput(
            attrs={
                "type": "checkbox",
                "data-switch": "bool",
            }
        ),
    )

    def clean_usrn(self):
        usrn = self.cleaned_data.get("usrn")
        # Add your email validation logic here
        validator = EmailValidator()
        try:
            validator(usrn)
        except ValidationError:
            raise forms.ValidationError("Invalid email address")
        return usrn


class SMSSettingsForm(forms.Form):
    asid = forms.CharField(
        label="Account SID",
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "maxlength": "254",
            }
        ),
    )
    tokn = forms.CharField(
        label="Token",
        widget=forms.PasswordInput(  # Use PasswordInput for token to hide it
            attrs={
                "class": "form-control",
            }
        ),
    )
    phon = forms.CharField(  # Use CharField for phone number
        label="Phone Number",
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "maxlength": "15",  # Adjust the max length for phone numbers
            }
        ),
    )

    def clean_phon(self):
        phon = self.cleaned_data.get("phon")

        # Ensure that the phone number is a valid E.164 formatted number
        try:
            parsed_phon = phonenumbers.parse(phon, None)
            if not phonenumbers.is_valid_number(parsed_phon):
                raise forms.ValidationError("Invalid phone number.")
        except phonenumbers.phonenumberutil.NumberFormatException:
            raise forms.ValidationError("Invalid phone number.")

        return phon


class TTNSettingsForm(forms.Form):
    ttn_host = forms.CharField(
        label="TTN Host",
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "maxlength": "254",
            }
        ),
    )
    api_key = forms.CharField(
        label="API Key",
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "maxlength": "254",
            }
        ),
    )

    def clean_ttn_host(self):
        ttn_host = self.cleaned_data.get("ttn_host")
        try:
            socket.gethostbyname(ttn_host)
        except socket.gaierror:
            raise forms.ValidationError("Invalid TTN Host")
        return ttn_host
