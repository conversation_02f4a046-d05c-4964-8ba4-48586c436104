# Generated by Django 4.2.7 on 2024-05-02 15:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('device_manager', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Event',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('Danger', 'Danger'), ('Warning', 'Warning'), ('Update', 'Update')], help_text='Type of the Event', max_length=8)),
                ('desc', models.CharField(help_text='Description of the Event', max_length=255)),
                ('crat', models.DateTimeField(auto_now_add=True, help_text='Datetime of the Event')),
                ('devi', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device_manager.device')),
            ],
        ),
        migrations.CreateModel(
            name='NotificationSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rxup', models.BooleanField(default=True, help_text='Receive Updates')),
                ('rxwr', models.BooleanField(default=True, help_text='Receive Warnings')),
                ('rxdg', models.BooleanField(default=True, help_text='Receive Dangers')),
                ('mthd', models.CharField(choices=[('No Delivery', 'No Delivery'), ('Email', 'Email'), ('SMS', 'SMS'), ('Email and SMS', 'Email and SMS')], default='No Delivery', help_text='Notification delivery method.', max_length=16)),
                ('devs', models.ManyToManyField(blank=True, help_text='Receive from Devices', to='device_manager.device')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('read', models.BooleanField(default=False)),
                ('sent', models.BooleanField(default=False, help_text='Whether the notification was sent through a route or not.')),
                ('fail', models.CharField(default='No Error.', help_text='Reason behind delivery failure.')),
                ('evnt', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='notification_center.event')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
