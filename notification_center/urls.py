from django.urls import path

from .views import (
    DeliveryOptionsView,
    read,
    notification_delete,
    NotificationView,
    export_to_csv,
)

app_name = "notification_center"

urlpatterns = [
    path("list/", NotificationView.as_view(), name="list"),
    path("list/<str:notification_status>/", NotificationView.as_view(), name="list_status"),
    path(
        "delivery_options/<int:profile_id>/",
        DeliveryOptionsView.as_view(),
        name="delivery_options",
    ),
    path("delete/<int:notification_id>/", notification_delete, name="delete"),
    path(
        "read/<int:notification_id>/",
        read,
        name="read",
    ),
    path('export/csv/', export_to_csv, name='export_csv'),
    path('export/csv/<str:notification_status>/', export_to_csv, name='export_csv'),
]
