import logging
import os
import sys
import threading

import django
from django.contrib.contenttypes.models import ContentType
from django.contrib.gis.geos import Point, Polygon

django_project_path = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(django_project_path)

from collections import defaultdict
from datetime import timed<PERSON>ta

from django.utils import timezone

from device_manager.models import Device
from notification_center.models import Event

# Set up the Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "app.settings")

django.setup()

STATUS_LEVELS = ["Update", "Warning", "Danger"]
# it can be different for each event type
COOLDOWN_PERIOD_MINUTES = 60
EVENT_DELIVERY_OPTIONS = {
    "Warning": "rxwr",
    "Danger": "rxdg",
    "Info": "rxif",
    "Update": "rxup",
}

logger = logging.getLogger("app")
lock = threading.Lock()


def process_device_messages(device):
    from notification_center.models import Event

    try:
        with lock:
            # Fetch unsent events
            unsent_events = Event.objects.filter(devi=device, notifications__sent=False)
            # Deduplicate and keep only unique events
            unique_events, duplicated_events = deduplicate_events(unsent_events)

            # Get unique categorized events and recipients list for each delivery option
            categorized_events, categorized_recipients = categorize_events(
                unique_events
            )
            send_notifications(categorized_events, categorized_recipients, device)

            # Mark duplicates events as sent
            handle_duplicate_events(duplicated_events)
    except Exception as e:
        logger.error(f"Error processing device messages: {e}")


def deduplicate_events(events):
    seen_descriptions = set()
    unique_events = []
    duplicated_events = []

    for event in events:
        event_key = (event.devi, event.type, event.desc)
        if event_key not in seen_descriptions:
            unique_events.append(event)
            seen_descriptions.add(event_key)
        else:
            duplicated_events.append(event)
    return unique_events, duplicated_events


def handle_duplicate_events(duplicated_events):
    from notification_center.models import Notification

    """
    Mark duplicated events as sent (update `notifications__sent=True`)
    """
    try:
        if duplicated_events:
            for event in duplicated_events:
                Notification.objects.filter(evnt=event).update(sent=True)
    except Exception as e:
        logger.error(f"Error in handle_duplicate_events: {e}")


def categorize_events(events):
    from notification_center.models import NotificationSettings

    categorized_events = defaultdict(list)
    categorized_recipients = defaultdict(lambda: {"email": set(), "sms": set()})

    for event in events:
        categorized_events[event.type].append(event)
        delivery_option = EVENT_DELIVERY_OPTIONS.get(event.type)

        if delivery_option:
            email_recipients = NotificationSettings.objects.filter(
                devs=event.devi,
                mthd__in=["Email", "Email and SMS"],
                **{delivery_option: True},
            ).values_list("user__email", flat=True)
            categorized_recipients[event.type]["email"].update(email_recipients)

            sms_recipients = (
                NotificationSettings.objects.filter(
                    devs=event.devi,
                    mthd__in=["SMS", "Email and SMS"],
                    **{delivery_option: True},
                )
                .exclude(user__userprofile__phon__isnull=True)
                .exclude(user__userprofile__phon__exact="")
                .values_list("user__userprofile__phon", flat=True)
            )
            categorized_recipients[event.type]["sms"].update(sms_recipients)

    return categorized_events, categorized_recipients


def send_notifications(categorized_events, categorized_recipients, device):
    from notification_center.models import Notification
    from scripts.communication import send_email_notification, send_sms_notification

    try:

        for event_type, events in categorized_events.items():
            if not events:
                continue

            email_recipients = list(categorized_recipients[event_type]["email"])
            sms_recipients = list(categorized_recipients[event_type]["sms"])

            events_to_send = should_send_notifications(device, event_type, events)

            email_sent = sms_sent = False

            if events_to_send:
                if email_recipients:
                    email_sent = send_email_notification(
                        event_type, device, email_recipients, events_to_send
                    )
                if sms_recipients:
                    sms_sent = send_sms_notification(
                        event_type, device, sms_recipients, events_to_send
                    )

                if email_sent and sms_sent:
                    Notification.objects.filter(
                        evnt__devi=device, evnt__in=events_to_send
                    ).update(sent=True)

    except Exception as e:
        logger.error(f"Error in send_notifications: {e}")


def should_send_notifications(device, event_type: str, events: list):
    from notification_center.models import Event

    try:
        time_cooldown = timezone.now() - timedelta(minutes=COOLDOWN_PERIOD_MINUTES)
        last_sent_event = (
            Event.objects.filter(devi=device, type=event_type, notifications__sent=True)
            .order_by("-crat")
            .first()
        )

        if (
            last_sent_event
            and last_sent_event.type == events[0].type
            and last_sent_event.desc == events[0].desc
        ):
            if last_sent_event.crat > time_cooldown:
                events.pop(0)

        return events
    except Exception as e:
        logger.error(f" Error in should_send_notifications: {e}")
        return events


def generate_global_events(device: Device):
    event_list=[]
    if device.batt < 5:
        event_list.append(Event(
            devi=device,
            type="Warning",
            desc="has low battery.",
        ))

        if device.stat != "Danger":
            device.stat = "Warning"

    if device.temp > 70:
        event_list.append(Event(
            devi=device,
            type="Warning",
            desc="has high temperature.",
        ))

        if device.stat != "Danger":
            device.stat = "Warning"
    process_events(event_list)

def check_gateway_node(device: Device, gateway: Device, outside_work_hours: bool):
    event_list=[]
    if device.name.startswith("GWN"):
        # Check for Main Power Disconnect
        if "Light" in device.attr["client"]:
            if device.attr["client"]["Light"] < 50:
                gateway.chrg = False

                # Calculate the time one hour ago from now
                now = timezone.now()
                one_hour_ago = now - timedelta(hours=1)

                # Check if an event with the specified description exists within the past hour
                if not Event.objects.filter(
                    devi=gateway,
                    type="Warning",
                    desc="disconnected from main power.",
                    crat__gte=one_hour_ago,  # Filter by creation time within the last hour
                ).exists():
                    event_list.append(Event(
                        devi=gateway,
                        type="Warning",
                        desc="disconnected from main power.",
                    ))

                if gateway.stat != "Danger":
                    gateway.stat = "Warning"
            else:
                if gateway.chrg == False:
                    gateway.chrg = True

                    event_list.append(Event(
                        devi=gateway,
                        type="Update",
                        desc="is back on main power.",
                    ))

        # Check Movement
        if (
            device.actv == False
            and outside_work_hours
            and device.attr["client"]["Motion event."] == True
        ):
            event_list.append(Event(
                devi=gateway,
                type="Danger",
                desc="has been moved.",
            ))

            gateway.stat = "Danger"
            gateway.actv = True
            device.stat = "Danger"
            device.actv = True

        elif (
            device.actv == True
            and outside_work_hours
            and (
                device.attr["client"]["Motionless event."] == True
                or device.attr["client"]["Motion event."] == False
            )
        ):
            event_list.append(Event(
                devi=gateway,
                type="Update",
                desc="is stationary again.",
            ))

            gateway.actv = False
            device.actv = False

        elif (
            device.actv == True
            and outside_work_hours
            and device.attr["client"]["Motion event."] == True
        ):
            event_list.append(Event(
                devi=gateway,
                type="Danger",
                desc="is still moving.",
            ))

            gateway.stat = "Danger"
            device.stat = "Danger"

        # Check Shock
        if outside_work_hours and device.attr["client"]["Shock event."] == True:
            event_list.append(Event(
                devi=gateway,
                type="Danger",
                desc="detected a shock.",
            ))

            gateway.stat = "Danger"
            device.stat = "Danger"

        # Check for Temperature
        gateway.temp = device.temp
        gateway.attr["client"]["temp"] = int(device.temp)

        if gateway.temp > 60:
            event_list.append(Event(
                devi=gateway,
                type="Warning",
                desc="has high temperature.",
            ))

            if gateway.stat != "Danger":
                gateway.stat = "Warning"
    process_events(event_list)


def check_back_online(*devices: Device):
    events=[]
    for device in devices:
        if device.stat == "Offline":
            events.append(Event(
                devi=device,
                type="Update",
                desc="is back online.",
            ))
    process_events(events)

def process_events(event_list):
    """
    Process events and handle duplicates while ensuring thread safety.
    """
    #Event.objects.bulk_create(event_list, ignore_conflicts=True)
    for event in event_list:
        event.save()


# def _check_charging_status(device):
#     if device.chrg == False and :
#         Event(
#             devi=device,
#             type="Warning",
#             desc="is disconnected from power, running on battery.",
#         ).save()

#         if device.stat != "Danger":
#             device.stat = "Warning"

#         device.chrg = True


def check_triggers(device: Device, outside_work_hours: bool):
    event_list=[]
    # Check Movement
    if (
        device.actv == False
        and outside_work_hours
        and device.attr["client"]["Motion event."] == True
    ):
        event_list.append(Event(
            devi=device,
            type="Danger",
            desc="has been moved.",
        ))

        device.stat = "Danger"
        device.actv = True

    elif (
        device.actv == True
        and outside_work_hours
        and (
            device.attr["client"]["Motionless event."] == True
            or device.attr["client"]["Motion event."] == False
        )
    ):
        event_list.append(Event(
            devi=device,
            type="Update",
            desc="is stationary again.",
        ))

        device.actv = False

    elif (
        device.actv == True
        and outside_work_hours
        and device.attr["client"]["Motion event."] == True
    ):
        event_list.append(Event(
            devi=device,
            type="Danger",
            desc="is still moving.",
        ))

        device.stat = "Danger"

    # Check Shock
    if outside_work_hours and device.attr["client"]["Shock event."] == True:
        event_list.append(Event(
            devi=device,
            type="Danger",
            desc="detected a shock.",
        ))

        device.stat = "Danger"
    process_events(event_list)


def check_out_of_field(device):
    event_list=[]
    assigned_field = device.fild
    device_location = Point(float(device.loca["lati"]), float(device.loca["long"]))

    field_polygon_coordinates = assigned_field.cord

    # Extract the coordinates from the dictionary and convert to a list
    field_coordinates_list = [
        (coord["lng"], coord["lat"]) for coord in field_polygon_coordinates
    ]
    # Swap the order of latitude and longitude
    field_coordinates_list = [(lng, lat) for lat, lng in field_coordinates_list]
    # Add the first point at the end to close the ring
    field_coordinates_list.append(field_coordinates_list[0])

    field_polygon = Polygon(field_coordinates_list)

    if not field_polygon.contains(device_location):
        if device.stat != "Danger":
            device.stat = "Warning"

        event_list.append(Event(
            devi=device,
            type="Warning",
            desc="is out of assigned field.",
        ))

        if not device.loca["oofi"]:
            device.loca["oofi"] = True
    else:
        if device.loca["oofi"]:
            event_list.append(Event(
                devi=device,
                type="Update",
                desc="is back in field of operation.",
            ))

            device.loca["oofi"] = False
    process_events(event_list)