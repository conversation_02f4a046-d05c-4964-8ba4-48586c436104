import csv
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse_lazy
from django.views.generic import FormView, ListView

from accounts.models import UserProfile
from notification_center.forms import NotificationSettingsForm

from .models import Notification, NotificationSettings


class NotificationView(LoginRequiredMixin, ListView):
    model = Notification
    template_name = "notification_center/list.html"
    context_object_name = "list"
    paginate_by = 10 

    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user
        if "notification_status" in self.kwargs:
            notification_status = self.kwargs["notification_status"]
            if notification_status == "read":
                return queryset.filter(user=user, read=True)
            elif notification_status == "unread":
                return queryset.filter(user=user, read=False)
        return queryset.filter(user=user)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context



class DeliveryOptionsView(LoginRequiredMixin, FormView):
    template_name = "notification_center/delivery_options.html"
    form_class = NotificationSettingsForm

    def get_success_url(self):
        return reverse_lazy(
            "notification_center:delivery_options",
            kwargs={"profile_id": self.kwargs["profile_id"]},
        )

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        profile_id = self.kwargs.get("profile_id")
        profile = get_object_or_404(UserProfile, id=profile_id)
        user = profile.user
        kwargs["instance"], _ = NotificationSettings.objects.get_or_create(user=user)
        return kwargs

    def form_valid(self, form):
        form.save()
        messages.success(self.request, "Settings saved successfully.")
        self.request.session["form_saved"] = True
        return super().form_valid(form)


@login_required
def notification_delete(request, notification_id):
    object = get_object_or_404(Notification, id=notification_id)
    object.delete()
    return redirect("notification_center:list")


@login_required
def read(request, notification_id):
    Notification.objects.filter(
        user=request.user,
        read=False,
        **({"id": notification_id} if notification_id else {})
    ).update(read=True)
    return HttpResponse(status=200)

def export_to_csv(request):
    # Create the HttpResponse object with the appropriate CSV header.
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="notification_export.csv"'

    writer = csv.writer(response)
    
    # Write header row (field names)
    writer.writerow(['Field', 'Device', 'Event Type','Description','Date & Time'])

    # Filter notifications based on notification_status
    queryset = Notification.objects.filter(user=request.user)

    if request.GET['notification_status'] == 'read':
        queryset = queryset.filter(read=True)
    elif request.GET['notification_status'] == 'unread':
        queryset = queryset.filter(read=False)

    # Write data rows
    for obj in queryset:
        writer.writerow([obj.evnt.devi.fild.name, obj.evnt.devi.name, obj.evnt.type,obj.evnt.desc,obj.evnt.crat])

    return response
