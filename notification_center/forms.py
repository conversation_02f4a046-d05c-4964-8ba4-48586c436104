from django import forms
from .models import NotificationSettings


class NotificationSettingsForm(forms.ModelForm):
    class Meta:
        model = NotificationSettings
        fields = ["rxif", "rxup", "rxwr", "rxdg", "devs", "mthd"]
        labels = {
            "rxif": "Info",
            "rxup": "Updates",
            "rxwr": "Warnings",
            "rxdg": "Danger",
            "devs": "Devices",
            "mthd": "Delivery Method",
        }
        widgets = {
            "rxif": forms.CheckboxInput(
                attrs={"type": "checkbox", "data-switch": "info"}
            ),
            "rxup": forms.CheckboxInput(
                attrs={"type": "checkbox", "data-switch": "primary"}
            ),
            "rxwr": forms.CheckboxInput(
                attrs={"type": "checkbox", "data-switch": "warning"}
            ),
            "rxdg": forms.CheckboxInput(
                attrs={"type": "checkbox", "data-switch": "danger"}
            ),
            "devs": forms.SelectMultiple(
                attrs={
                    "class": "select2 form-control select2-multiple",
                    "data-toggle": "select2",
                    "multiple": "multiple",
                    "data-placeholder": "Choose ...",
                }
            ),
            "mthd": forms.Select(
                attrs={
                    "class": "form-control",
                    "data-toggle": "form-select",
                }
            ),
        }
