from device_manager.models import Device
from device_manager.views import get_last_update
from fields.models import Field
from django.http import JsonResponse
from django.views.generic import ListView
from django.conf import settings
from django.contrib.auth.decorators import login_required
from notification_center.models import Event
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q
from django.forms.models import model_to_dict

api_key = settings.GOOGLE_MAPS_API_KEY


class NetworkOverview(LoginRequiredMixin, ListView):
    model = Field
    template_name = "network_monitor/overview.html"
    context_object_name = "list"

    def get_queryset(self):
        queryset = super().get_queryset()

        user = self.request.user

        if user.is_superuser:
            return queryset.order_by("name")
        return (
            queryset.filter(devices__userprofile__user=user).distinct().order_by("name")
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["GOOGLE_MAPS_API_KEY"] = settings.GOOGLE_MAPS_API_KEY

        queryset = context["list"]

        # Get the selected field ID from the request GET parameters
        selected_field_id = self.request.GET.get("field_id")

        # pass fields query set as list of ids
        context["field_ids"] = list(queryset.values_list("id", flat=True))

        # Pass the selected field ID to the template context
        context["selected_field_id"] = selected_field_id if selected_field_id else 0

        return context
