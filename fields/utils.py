import logging
from datetime import datetime
from .models import Field
from django.utils import timezone
from django.core.exceptions import ValidationError


logger = logging.getLogger("app")


def is_event_within_work_shift(field: Field, event_timestamp) -> bool:
    try:
        # get the day of the week and the field of the device
        event_timestamp = datetime.fromisoformat(event_timestamp)
        muscat_datetime = timezone.localtime(event_timestamp)
        event_time_muscat = muscat_datetime.time().strftime('%H:%M')
        today = muscat_datetime.strftime("%A").lower()

        # Get the work shifts for the today
        work_shifts = field.work_shifts.get(today.lower(), [])

        # Check if the current time falls within any of the work shift ranges
        for shift in work_shifts:
            start_time, end_time = shift.split("-")
            if start_time <= end_time:  # Normal range
                if start_time <= event_time_muscat <= end_time:
                    return True
            else:
                logger.error("Error in is_event_within_work_shift: start time cannot be greater than end time")

        return False
    except Exception as e:
        logger.error(f"Error in is_event_within_work_shift: {e}")
        return False


def prepare_work_shifts_context(field):
    work_shifts = []
    for day, shifts in field.work_shifts.items():
        for shift in shifts:
            start_time, end_time = shift.split("-")
            work_shifts.append(
                {
                    "day": day,
                    "start_time": start_time,
                    "end_time": end_time,
                }
            )
    return sort_work_shifts(work_shifts)

def sort_work_shifts(work_shifts):
    # Define the custom order for days of the week
    day_order = {
        "sunday": 0,
        "monday": 1,
        "tuesday": 2,
        "wednesday": 3,
        "thursday": 4,
        "friday": 5,
        "saturday": 6,
    }

    # Sort work_shifts by day and then by start and end times
    sorted_work_shifts = sorted(
        work_shifts,
        key=lambda shift: (
            day_order[shift["day"]],  # Sort by day using the custom order
            shift["start_time"],      # Sort by start time
            shift["end_time"],        # Sort by end time
        ),
    )
    return sorted_work_shifts


def get_work_shifts_from_form(request):
    work_shifts = {}
    days = request.POST.getlist("day")
    start_times = request.POST.getlist("start_time")
    end_times = request.POST.getlist("end_time")
    for day, start, end in zip(days, start_times, end_times):
        shift = f"{start}-{end}"
        if start > end :
            raise ValidationError(f"Start time {start} cannot be greater than or equal to end time {end} for {day.capitalize()}.")
        work_shifts.setdefault(day, []).append(shift)
    return work_shifts
