from django import forms

from .models import Field


class FieldForm(forms.ModelForm):
    work_shifts = forms.JSONField(required=False)

    class Meta:
        model = Field
        fields = ("name", "cord", "colr", "covr", "loca")
        labels = {
            "name": "Name",
            "colr": "Highlight Color",
        }
        widgets = {
            "name": forms.TextInput(attrs={"class": "form-control col-sm-2"}),
            "cord": forms.Textarea(attrs={"class": "form-control col-sm-2 d-none"}),
            "covr": forms.NumberInput(attrs={"class": "form-control col-sm-2 d-none"}),
            "colr": forms.TextInput(
                attrs={"class": "form-control col-sm-2", "type": "color"}
            ),
            "loca": forms.TextInput(attrs={"class": "form-control col-sm-2 d-none"}),
        }
