# Generated by Django 4.2.7 on 2024-05-02 15:37

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Field',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(help_text='Enter the name of the field.', max_length=255)),
                ('cord', models.JSONField(help_text='Enter the coordinates of the field as a list of JSON objects.')),
                ('colr', models.Char<PERSON>ield(help_text='Enter the hex color code for the field highlight.', max_length=7)),
                ('covr', models.DecimalField(decimal_places=3, default=0, help_text='Area of coverage in km2.', max_digits=20)),
                ('loca', models.CharField(default='Muscat', help_text='Location of the field.')),
            ],
        ),
    ]
