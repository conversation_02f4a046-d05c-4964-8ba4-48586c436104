import json
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.forms.models import model_to_dict


# Create your models here.
class Field(models.Model):
    name = models.CharField(max_length=255, help_text="Enter the name of the field.")
    cord = models.JSONField(
        help_text="Enter the coordinates of the field as a list of JSON objects."
    )
    colr = models.CharField(
        max_length=7, help_text="Enter the hex color code for the field highlight."
    )
    covr = models.DecimalField(
        decimal_places=3, max_digits=20, default=0, help_text="Area of coverage in km2."
    )
    loca = models.CharField(default="Muscat", help_text="Location of the field.")
    work_shifts = models.JSONField(default=dict, blank=True)

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "cord": self.cord,
            "colr": self.colr,
            "covr": float(self.covr),
            "loca": self.loca,
            "work_shifts": self.work_shifts,
        }

    def to_json(self):
        """
        Serializes the Field object to a JSON string.
        """
        return json.dumps(self.to_dict())

    @staticmethod
    def from_json(json_data):
        """
        Deserializes a JSON string to a Field object.
        """
        data = json.loads(json_data)
        field = Field(
            id=data.get("id"),
            name=data.get("name"),
            cord=data.get("cord"),
            colr=data.get("colr"),
            covr=data.get("covr"),
            loca=data.get("loca"),
            work_shifts=data.get("work_shifts"),
        )
        return field

    def __str__(self):
        return self.name


@receiver(post_save, sender=Field)
def send_update(sender, instance: Field, **kwargs):
    channel_layer = get_channel_layer()
    field_group_name = f"field_{instance.id}"

    async_to_sync(channel_layer.group_send)(
        field_group_name,
        {"type": "object_update", "data": {f"{instance.id}": instance.to_dict()}},
    )
