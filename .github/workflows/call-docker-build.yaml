---
  # Template source: https://github.com/esbaar-ai/docker-build-workflow/blob/main/templates/call-docker-build.yaml
  name: Docker Build and Push
  
  on:
    release:
      types: [prereleased, released]
    push:
      branches:
        - main
        - dev
      # Don't rebuild image if someone only edited unrelated files
      paths-ignore:
        - "README.md"
        - ".github/linters/**"
        - ".djlintrc"
        - "logs/"
        - "Makefile"
        - ".gitignore"
    pull_request:
      # Don't rebuild image if someone only edited unrelated files
      paths-ignore:
        - "README.md"
        - ".github/linters/**"
        - ".djlintrc"
        - "logs/"
        - "Makefile"
        - ".gitignore"
  
  # Cancel any previously-started, yet still active runs of this workflow on the same branch
  concurrency:
    group: ${{ github.ref }}-${{ github.workflow }}
    cancel-in-progress: true
  
  jobs:
    call-docker-build:
      name: Call Docker Build and Push
  
      uses: ESBAAR-AI/docker-build-workflow/.github/workflows/reusable-docker-build.yaml@main
  
      permissions:
        contents: read
        packages: write # Needed to push docker image to ghcr.io
        pull-requests: write # Needed to create and update comments in PRs
  
  
      # # Uncomment following if your repository has submodules that require cloning
      # secrets:
      #   repo-readonly-github-app-id: ${{ secrets.REPO_READONLY_GITHUB_APP_ID }}
      #   repo-readonly-github-app-key: ${{ secrets.REPO_READONLY_GITHUB_APP_KEY }}
  
      # Uncomment following if you require inputs to be passed to the workflow
      with:
      #   # The docker build context to us. If you want to set to a subdirectory, use format of {{defaultContext}}:mysubdir
      #   context:
        # The Dockerfile to build, relative to the context path
        file: Dockerfile
      #   # Specify build target within Dockerfile
      #   target:
