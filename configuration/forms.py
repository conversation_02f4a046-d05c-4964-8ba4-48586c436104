import json
from django import forms


class NetworkConfigurationForm(forms.Form):
    txpw = forms.IntegerField(
        label="Transmit Power",
        min_value=0,
        initial=15,
        widget=forms.NumberInput(
            attrs={"class": "form-control col-sm-2", "placeholder": "dBm"}
        ),
    )
    pram = forms.IntegerField(
        label="Preamble Length",
        min_value=0,
        initial=8,
        widget=forms.NumberInput(
            attrs={"class": "form-control col-sm-2", "placeholder": "bits"}
        ),
    )
    duty = forms.IntegerField(
        label="Duty Cycle",
        initial=10,
        widget=forms.NumberInput(
            attrs={"class": "form-control col-sm-2", "placeholder": "%"}
        ),
    )
    lbtk = forms.BooleanField(
        label="Listen Before Talk",
        widget=forms.CheckboxInput(
            attrs={
                "type": "checkbox",
                "data-switch": "primary",
            }
        ),
        required=False,
    )
    retx = forms.IntegerField(
        label="Retransmissions",
        min_value=0,
        max_value=15,
        initial=3,
        widget=forms.NumberInput(
            attrs={"class": "form-control col-sm-2", "placeholder": "times"}
        ),
    )
    rwln = forms.IntegerField(
        label="Receive Window Length",
        min_value=1,
        max_value=6,
        initial=3,
        widget=forms.NumberInput(
            attrs={"class": "form-control col-sm-2", "placeholder": "seconds"}
        ),
    )
    rwdl = forms.IntegerField(
        label="Receive Window Delay",
        min_value=1,
        max_value=6,
        initial=2,
        widget=forms.NumberInput(
            attrs={"class": "form-control col-sm-2", "placeholder": "seconds"}
        ),
    )

    # generate rest of the channels up to 15
    for i in range(0, 16):
        channel_index = str(i).zfill(2)
        exec(
            f"ch{channel_index}_enable = forms.BooleanField(label='Channel {i} Enable', initial=True, required=False, widget=forms.CheckboxInput(attrs={{'hidden': 'true'}}))"
        )
        exec(
            f"ch{channel_index}_value = forms.IntegerField(label='Channel {i} Value', initial={866300000 + (200000 * i)}, min_value=863000000, max_value=870000000, widget=forms.NumberInput(attrs={{'class': 'form-control', 'placeholder': 'Hz'}}))"
        )


class WhiskersNodeV2ConfigurationForm(forms.Form):
    upin = forms.IntegerField(
        label="Update Interval",
        min_value=1,
        max_value=24,
        initial=1,
        widget=forms.NumberInput(
            attrs={"class": "form-control", "placeholder": "hours"}
        ),
    )
    atim = forms.IntegerField(
        label="Activity Time",
        min_value=1,
        max_value=255,
        initial=6,
        widget=forms.NumberInput(
            attrs={"class": "form-control", "placeholder": "samples"}
        ),
    )
    athr = forms.IntegerField(
        label="Activity Threshold (G-force)",
        min_value=0,
        max_value=4095,
        initial=112,
        widget=forms.NumberInput(attrs={"class": "form-control", "placeholder": "mg"}),
    )
    ithr = forms.IntegerField(
        label="Inactivity Threshold (G-force)",
        min_value=0,
        max_value=4095,
        initial=80,
        widget=forms.NumberInput(attrs={"class": "form-control", "placeholder": "mg"}),
    )
    adet = forms.BooleanField(
        label="Activity Detection",
        initial=True,
        widget=forms.CheckboxInput(
            attrs={"type": "checkbox", "data-switch": "primary", "checked": "checked"}
        ),
        required=False,
    )


class WhiskersGatewayV2ConfigurationForm(forms.Form):
    upin = forms.IntegerField(
        label="Update Interval",
        min_value=1,
        max_value=24,
        initial=24,
        widget=forms.NumberInput(
            attrs={"class": "form-control", "placeholder": "hours"}
        ),
    )
    atim = forms.IntegerField(
        label="Activity Time",
        min_value=1,
        max_value=255,
        initial=6,
        widget=forms.NumberInput(
            attrs={"class": "form-control", "placeholder": "samples"}
        ),
    )
    athr = forms.IntegerField(
        label="Activity Threshold (G-force)",
        min_value=0,
        max_value=2000,
        initial=1000,
        widget=forms.NumberInput(attrs={"class": "form-control", "placeholder": "mg"}),
    )
    adet = forms.BooleanField(
        label="Activity Detection",
        widget=forms.CheckboxInput(
            attrs={"type": "checkbox", "data-switch": "primary"},
        ),
        required=False,
    )
