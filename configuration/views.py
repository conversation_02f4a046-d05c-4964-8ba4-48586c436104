import sys

from .forms import *
from django.views.generic import FormView
from django.urls import reverse_lazy
from django.shortcuts import get_object_or_404, redirect
from django.conf import settings
from django.utils.decorators import method_decorator
from django.contrib.auth.decorators import user_passes_test
from django.contrib.contenttypes.models import ContentType
from django.contrib import messages

# don't remove
from device_manager.models import Device


# Create your views here.
@method_decorator(user_passes_test(lambda u: u.is_superuser), name="dispatch")
class ApplicationConfigurationView(FormView):
    form_class = WhiskersNodeV2ConfigurationForm
    template_name = "configuration/whiskers_node_v2_config.html"
    next = None

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context

    def get_success_url(self):
        # Set the success URL based on request parameters
        device_id = self.kwargs["device_id"]
        return reverse_lazy(
            "configure:application",
            kwargs={"device_id": device_id},
        )

    def get_initial(self):
        initial = super().get_initial()

        device_id = self.kwargs["device_id"]

        # Retrieve the device
        self.device = get_object_or_404(Device, id=device_id)

        old_config = self.device.acfg

        # get form and template based on device type
        self.form_class = globals()[
            self.device.type.replace(" ", "") + "ConfigurationForm"
        ]
        self.template_name = (
            "configuration/app_configs/"
            + self.device.type.replace(" ", "_").lower()
            + "_config.html"
        )

        self.synchronization_status = {}

        self.is_synchronized = True

        for key, value in old_config.items():
            # if respective field is checkbox, set checked to True in widget attrs
            if self.form_class.base_fields[key].widget.input_type == "checkbox":
                initial[key] = value["new"]
                self.form_class.base_fields[key].widget.attrs.update(
                    {"checked": True if value["new"] == True else False}
                )
            else:
                initial[key] = value["new"]
            self.synchronization_status.update(
                {key: True if value["sync"] == value["new"] else False}
            )

        if False in self.synchronization_status.values():
            self.is_synchronized = False

        return initial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Add the synchronization status to the context
        context["is_synchronized"] = self.is_synchronized
        context["synchronization_status"] = self.synchronization_status
        context["name"] = self.device.name

        if self.asset == "Device":
            context["icon_url"] = (
                "/static/images/device/icons/"
                + self.device.aset.lower().replace(" ", "-")
                + ".png"
            )

        return context

    def form_valid(self, form):
        if form.is_valid():
            if self.device.acfg:
                for key, value in self.device.acfg.items():
                    self.device.acfg[key]["new"] = form.cleaned_data[key]
                    self.synchronization_status.update(
                        {key: True if value["sync"] == value["new"] else False}
                    )
            else:
                for key, value in form.cleaned_data.items():
                    # is_sync set to False is value is not default, else True
                    self.device.acfg.update({key: {"new": value, "sync": value}})
                    self.synchronization_status.update(
                        {key: True if value["sync"] == value["new"] else False}
                    )

            for key, value in self.synchronization_status.items():
                if value == False:
                    self.is_synchronized = False
                    break

            self.device.save()

            if self.is_synchronized:
                messages.success(
                    self.request,
                    "Configuration saved successfully. Configuration is Synchronized!",
                )
            else:
                messages.warning(
                    self.request,
                    "Configuration saved successfully. Waiting for Synchronization...",
                )

        else:
            messages.warning(
                self.request,
                "No changes were made!",
            )

        return redirect(self.get_success_url())


@method_decorator(user_passes_test(lambda u: u.is_superuser), name="dispatch")
class NetworkConfigurationView(FormView):
    form_class = NetworkConfigurationForm
    template_name = "configuration/net_config.html"
    next = None

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context

    def get_success_url(self):
        # Set the success URL based on request parameters
        device_id = self.kwargs["device_id"]
        return reverse_lazy(
            "configure:network",
            kwargs={"device_id": device_id},
        )

    def get_initial(self):
        # Initialize a dictionary to hold initial values for form fields
        initial = super().get_initial()

        device_id = self.kwargs["device_id"]

        # Retrieve the device
        self.device = get_object_or_404(Device, id=device_id)

        # Set initial values for specific form fields based on old_config
        fields_to_update = [
            "txpw",
            "duty",
            "pram",
            "retx",
            "lbtk",
            "rwln",
            "rwdl",
        ]

        self.synchronization_status = {}

        channels_mask = self.device.ncfg.get("cmsk", {}).get("new", [])
        self.synchronization_status.update(
            {
                "cmsk": (
                    True
                    if self.device.ncfg.get("cmsk", {}).get("sync")
                    == self.device.ncfg.get("cmsk", {}).get("new")
                    else False
                )
            }
        )

        for field_name in fields_to_update:
            initial[field_name] = self.device.ncfg.get(field_name, {}).get("new")
            self.synchronization_status.update(
                {
                    field_name: (
                        True
                        if self.device.ncfg.get(field_name, {}).get("sync")
                        == self.device.ncfg.get(field_name, {}).get("new")
                        else False
                    )
                }
            )

        # generate channel key names
        channels_to_update = [f"ch{i:02}" for i in range(16)]

        for channel_name in channels_to_update:
            channel_number = int(channel_name[-2:])
            initial[channel_name + "_enable"] = channels_mask[channel_number]
            initial[channel_name + "_value"] = self.device.ncfg.get(
                channel_name, {}
            ).get("new")
            self.synchronization_status.update(
                {
                    channel_name: (
                        True
                        if self.device.ncfg.get(channel_name, {}).get("sync")
                        == self.device.ncfg.get(channel_name, {}).get("new")
                        else False
                    )
                }
            )

        self.is_synchronized = True

        if False in self.synchronization_status.values():
            self.is_synchronized = False

        return initial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Add the synchronization status to the context
        context["is_synchronized"] = self.is_synchronized
        context["synchronization_status"] = self.synchronization_status
        return context

    # form valid based on how get_initial was modelled, but reversed
    def form_valid(self, form):
        if form.is_valid():
            # Set initial values for specific form fields based on old_config
            fields_to_update = [
                "txpw",
                "duty",
                "pram",
                "lbtk",
                "retx",
                "rwln",
                "rwdl",
            ]

            # generate channel key names
            channels_to_update = [f"ch{i:02}" for i in range(16)]

            for field_name in fields_to_update:
                self.device.ncfg[field_name]["new"] = form.cleaned_data[field_name]
                self.synchronization_status.update(
                    {
                        field_name: (
                            True
                            if self.device.ncfg[field_name]["sync"]
                            == self.device.ncfg[field_name]["new"]
                            else False
                        )
                    }
                )

            for channel_name in channels_to_update:
                channel_number = int(channel_name[-2:])
                self.device.ncfg[channel_name]["new"] = form.cleaned_data[
                    channel_name + "_value"
                ]
                self.device.ncfg["cmsk"]["new"][channel_number] = form.cleaned_data[
                    channel_name + "_enable"
                ]
                self.synchronization_status.update(
                    {
                        channel_name: (
                            True
                            if self.device.ncfg[channel_name]["sync"]
                            == self.device.ncfg[channel_name]["new"]
                            else False
                        )
                    }
                )
                self.synchronization_status.update(
                    {
                        "cmsk": (
                            True
                            if self.device.ncfg["cmsk"]["sync"]
                            == self.device.ncfg["cmsk"]["new"]
                            else False
                        )
                    }
                )

            for key, value in self.synchronization_status.items():
                if value == False:
                    self.is_synchronized = False
                    break

            if self.is_synchronized:
                messages.success(
                    self.request,
                    "Configuration saved successfully. Configuration is Synchronized!",
                )
            else:
                messages.warning(
                    self.request,
                    "Configuration saved successfully. Waiting for Synchronization...",
                )

            self.device.save()

        return redirect(self.get_success_url())

    def form_invalid(self, form):
        return super().form_invalid(form)
