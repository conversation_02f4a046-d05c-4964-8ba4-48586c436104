{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
  Devices
{% endblock title %}
{% block content %}
  <!-- Start Content-->
  <div id="network-overview" class="container-fluid">
    <!-- start page title -->
    <div class="row">
      <div class="col-lg">
        <div class="page-title-box">
          <div class="col-lg">
            <div class="page-title-box d-flex align-items-center">
              <h4 class="page-title">Field</h4>
              <div class="form-group mb-0 ms-3">
                <div class="input-group">
                  <select class="form-select" id="field-selection">
                    <option value="0" {% if selected_field_id == "0" %}selected{% endif %}>All</option>
                    {% for field in list %}<option value="{{ field.id }}">{{ field.name }}</option>{% endfor %}
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- end page title -->
    <!-- Recent Acitivty, Asset List, & Network Map -->
    <div class="row">
      <!-- Recent Acitivty & Asset List -->
      <div class="col-xl-3">
        <!-- Asset List -->
        <div class="card" style="height: 35vh;">
          <div class="card-body border-top-0 border-start-0 border-end-0 border-dashed border"
               style="max-height: 0px">
            <h3 class="header-title">Assets</h3>
          </div>
          <div class="card-body pt-0" data-simplebar style="height: 28vh">
            <div id="asset-list-container"></div>
          </div>
        </div>
        <!-- Recent Activity -->
        <div class="card" style="height: 35vh;">
          <div class="card-body" style="max-height: 0px">
            <h3 class="header-title">
              Recent Activity <span id="ws-connection" class="badge">Disconnected</span>
            </h3>
          </div>
          <div class="card-header bg-light-lighten border-top border-bottom border-light py-1 text-center">
            <div class="d-flex align-items-center justify-content-center">
              <i class="mdi mdi-access-point mdi-18px px-1"></i>
              <p class="m-0">
                Last update <b id="last-update">—</b> ago
              </p>
            </div>
          </div>
          <div class="card-body pt-2" data-simplebar style="height: 22vh">
            <div class="timeline-alt py-0">
              <div id="recent-activity-container"></div>
            </div>
          </div>
        </div>
      </div>
      <!-- Network Map -->
      <div class="col-xl-9">
        <div class="card">
          <div class="card-body">
            <h3 class="header-title">Map</h3>
            <div id="device_map" class="gmaps"  style="height: 64.5vh"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}
{% block extra_javascript %}
  <!-- Third party js -->
  <script src="{% static 'js/custom/dashboards/common/device_map.js' %}"></script>
  <script src="https://maps.googleapis.com/maps/api/js?key={{ GOOGLE_MAPS_API_KEY }}&callback=initMap"
          defer></script>
  <!-- App js -->
  <script>
    // load field ids
    const fieldIds = {{ selected_field_id }} ? [{{ selected_field_id }}] : {{ field_ids }};
    const selectedFieldId = {{ selected_field_id }};
  </script>
  <script src="{% static 'js/custom/select_field.js' %}"></script>
  <script src="{% static 'js/custom/dashboards/common/events_list_generator.js' %}"></script>
  <script src="{% static 'js/custom/dashboards/common/time_elapsed.js' %}"></script>
  <script src="{% static 'js/custom/dashboards/overview.js' %}"></script>
  <script src="{% static 'js/custom/dashboards/common/fetch_fields.js' %}"></script>
{% endblock extra_javascript %}
