{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
    Device Overview
{% endblock title %}
{% block content %}
    <!-- Start Content-->
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row">
            <div class="col-lg">
                <div class="page-title-box">
                    <h4 class="page-title">
                        <a href="#" onclick="javascript:history.back()" class="link-secondary"><i class="dripicons-arrow-thin-left"></i></a> {{ device.name }}
                    </h4>
                </div>
            </div>
            <div class="col-lg-auto">
                <div class="modal-footer d-flex justify-content-center align-items-center px-0">
                    <a href="{% url 'device_manager:edit' device_id=device.id %}"
                       class="btn btn-secondary rounded-pill {% if not user.is_superuser %}disabled{% endif %}">
                        <i class="mdi mdi-pencil mdi-18px"></i>
                    </a>
                </div>
            </div>
            <div class="col-lg-auto">
                <div class="modal-footer d-flex justify-content-center align-items-center px-0">
                    <a href="#"
                       data-toggle="modal"
                       data-target="#hideModal"
                       class="btn {% if device.hidn == False %}btn-success{% else %}btn-outline-success{% endif %} rounded-pill {% if not user.is_superuser %}disabled{% endif %}">
                        <i class="mdi {% if device.hidn == False %}mdi-eye{% else %}mdi-eye-off{% endif %} mdi-18px"></i>
                    </a>
                </div>
            </div>
            <div class="col-lg-auto">
                <div class="modal-footer d-flex justify-content-center align-items-center px-0">
                    <a href="#"
                       data-toggle="modal"
                       data-target="#clearModal"
                       class="btn btn-primary rounded-pill">
                        <i class="mdi mdi-refresh mdi-18px"></i>
                    </a>
                </div>
            </div>
            <div class="col-lg-auto">
                <div class="modal-footer d-flex justify-content-center align-items-center px-0">
                    <a href="#"
                       data-toggle="modal"
                       data-target="#maintenanceModal"
                       class="btn {% if device.mntc == True %}btn-warning{% else %}btn-outline-warning{% endif %} rounded-pill">
                        <i class="mdi {% if device.mntc == True %}mdi-cog{% else %}mdi-cog{% endif %} mdi-18px"></i>
                    </a>
                </div>
            </div>
            <div class="col-lg-auto">
                <div class="modal-footer d-flex justify-content-center align-items-center px-0">
                    <a href="#"
                    data-toggle="modal"
                    data-target="#deleteModal"
                    class="btn btn-danger rounded-pill {% if not user.is_superuser %}disabled{% endif %}">
                    <i class="mdi mdi-delete mdi-18px"></i>
                </a>
            </div>
            </div>
            <!-- <div class="col-lg-auto">
                <div class="modal-footer d-flex justify-content-center align-items-center px-0">
                    <a href="{% url 'configure:network' device_id=device.id %}"
                    class="btn btn-success rounded-pill disabled">
                        <i class="mdi mdi-wifi-cog mdi-18px"></i>
                    </a>
                </div>
            </div>
            <div class="col-lg-auto">
                <div class="modal-footer d-flex justify-content-center align-items-center px-0">
                    <a href="{% url 'configure:application' device_id=device.id %}"
                    class="btn btn-info rounded-pill disabled">
                        <i class="mdi mdi-cog mdi-18px"></i>
                    </a>
                </div>
            </div> -->
        </div>
        <!-- Delete Modal -->
        <div class="modal fade"
             id="deleteModal"
             tabindex="-1"
             role="dialog"
             aria-labelledby="deleteModalLabel"
             aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="deleteModalLabel">Delete Confirmation</h4>
                    </div>
                    <div class="modal-body">
                        Are you sure you want to delete <b>{{ device.name }}</b>? This action cannot be undone.
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <a href="{% url 'device_manager:delete' device.id %}" method="POST">
                            <button type="button" class="btn btn-danger">Delete</button>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- Hide Modal -->
        <div class="modal fade"
             id="hideModal"
             tabindex="-1"
             role="dialog"
             aria-labelledby="hideModalLabel"
             aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="hideModalLabel">
                            {% if device.hidn == False %}
                                Hide Device
                            {% else %}
                                Show Device
                            {% endif %}
                        </h4>
                    </div>
                    <div class="modal-body">
                        Are you sure you want to
                        {% if device.hidn == False %}
                            hide
                        {% else %}
                            show
                        {% endif %}
                        <b>{{ device.name }}</b>
                        {% if device.hidn == True %}
                            on the main dashboard?
                        {% else %}
                            ? This will stop the device from showing on the main dashboard.
                        {% endif %}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <a href="{% url 'device_manager:toggle_hide' device.id %}" method="POST">
                            <button type="button" class="btn btn-success">Yes</button>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- Maintenance Modal -->
        <div class="modal fade"
             id="maintenanceModal"
             tabindex="-1"
             role="dialog"
             aria-labelledby="maintenanceModalLabel"
             aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="maintenanceModalLabel">
                            {% if device.mntc == True %}
                                {% if not user.is_superuser %}Request to{% endif %}
                                Disable Maintenance Mode
                            {% else %}
                                {% if not user.is_superuser %}Request to{% endif %}
                                Enable Maintenance Mode
                            {% endif %}
                        </h4>
                    </div>
                    <div class="modal-body">
                        {% if user.is_superuser %}
                            Are you sure you want to turn
                            {% if device.mntc == True %}
                                off
                            {% else %}
                                on
                            {% endif %}
                            maintenance mode for <b>{{ device.name }}</b>?
                        {% else %}
                            Request maintenance mode to be turned
                            {% if device.mntc == True %}
                                off
                            {% else %}
                                on
                            {% endif %}
                            for <b>{{ device.name }}</b>?
                        {% endif %}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <a href="{% url 'device_manager:toggle_maintenance' device.id %}"
                           method="POST">
                            <button type="button" class="btn btn-warning">Yes</button>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- Clear Modal -->
        <div class="modal fade"
             id="clearModal"
             tabindex="-1"
             role="dialog"
             aria-labelledby="clearModalLabel"
             aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="clearModalLabel">Clear Status?</h4>
                    </div>
                    <div class="modal-body">Are you sure you want to clear the device status? Device might still be in danger.</div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <a href="{% url 'device_manager:clear_status' device.id %}"
                           method="POST">
                            <button type="button" class="btn btn-primary">Yes</button>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- end page title -->
        <!-- Overview -->
        <div class="row">
            <div class="col-12">
                <div class="card widget-inline">
                    <div class="card-body p-0">
                        <div class="row g-0">
                            <div class="col-sm-6 col-lg-2">
                                <div class="card rounded-0 shadow-none m-0">
                                    <div class="card-body text-center">
                                        <i class="mdi mdi-progress-upload text-muted mdi-36px"></i>
                                        <h3>
                                            <span id="transmitted">0</span>
                                        </h3>
                                        <p class="text-muted font-15 mb-0">Transmitted</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6 col-lg-2">
                                <div class="card rounded-0 shadow-none m-0 border-start border-light">
                                    <div class="card-body text-center">
                                        <i class="mdi mdi-progress-clock text-muted mdi-36px"></i>
                                        <h3>
                                            <span id="pending">0</span>
                                        </h3>
                                        <p class="text-muted font-15 mb-0">Pending</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6 col-lg-4">
                                <div class="card rounded-0 shadow-none m-0 border-start border-light">
                                    <div class="card-body text-center">
                                        <i class="mdi mdi-progress-check text-muted mdi-36px"></i>
                                        <h3>
                                            <span id="acknowledgement">0</span>
                                        </h3>
                                        <p class="text-muted font-15 mb-0">Acknowlegement Rate</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6 col-lg-2">
                                <div class="card rounded-0 shadow-none m-0 border-start border-light">
                                    <div class="card-body text-center">
                                        <i class="mdi mdi-progress-download text-muted mdi-36px"></i>
                                        <h3>
                                            <span id="received">0</span>
                                        </h3>
                                        <p class="text-muted font-15 mb-0">Received</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6 col-lg-2">
                                <div class="card rounded-0 shadow-none m-0 border-start border-light">
                                    <div class="card-body text-center">
                                        <i class="mdi mdi-progress-check text-muted mdi-36px"></i>
                                        <h3>
                                            <span id="forwarded">0</span>
                                        </h3>
                                        <p class="text-muted font-15 mb-0">Forwarded</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- end row -->
                    </div>
                </div>
                <!-- end card-box-->
            </div>
            <!-- end col-->
        </div>
        <div class="row ">
            <!-- Recent Activity & Sensors -->
            <div class="col-md-5">
                <div class="row">
                    <!-- Recent Activity -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body" style="max-height: 0px">
                                <h3 class="header-title">
                                    Recent Activity <span id="ws-connection" class="badge">Disconnected</span>
                                </h3>
                            </div>
                            <div class="card-header bg-light-lighten border-top border-bottom border-light py-1 text-center">
                                <div class="d-flex align-items-center justify-content-center">
                                    <i class="mdi mdi-access-point mdi-18px px-1"></i>
                                    <p class="m-0">
                                        Last update <b id="last-update">—</b> ago
                                    </p>
                                </div>
                            </div>
                            <div class="card-body pt-2" data-simplebar style="height: 59vh;">
                                <div class="timeline-alt py-0">
                                    <div id="recent-activity-container"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Battery & Temperature -->
                    <div class="col-md-6">
                        <!-- Battery -->
                        <div class="card">
                            <div class="d-flex card-header justify-content-between align-items-center">
                                <h4 class="header-title mt-1 mb-0">Battery</h4>
                                <div class="dropdown">
                                    {% if device.chrg %}
                                        <i class="mdi mdi-power-plug mdi-18px px-1"></i>
                                    {% else %}
                                        <i class="mdi mdi-power-plug-off mdi-18px px-1"></i>
                                    {% endif %}
                                    <a href="{% url 'telemetry:viewer' device_id=device.id key='batt' title='Battery' %}"
                                       class="card-drop p-0">
                                        <i class="mdi mdi-chart-timeline-variant"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="card-body p-0" style="height: 28vh;">
                                <div id="battery_indicator" class="apex-charts p-0"></div>
                            </div>
                        </div>
                        <!-- Temperature -->
                        <div class="card">
                            <div class="d-flex card-header justify-content-between align-items-center">
                                <h4 class="header-title mt-1 mb-0">Temperature</h4>
                                <div class="dropdown">
                                    <a href="{% url 'telemetry:viewer' device_id=device.id key='temp' title='Temperature' %}"
                                       class="card-drop p-0">
                                        <i class="mdi mdi-chart-timeline-variant"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="card-body p-0" style="height: 27vh;">
                                <div id="temperature">{{ device.temp }}</div>
                                <div id="thermometer" class="apex-charts p-0"></div>
                            </div>
                            <!-- end card-body-->
                        </div>
                    </div>
                </div>
            </div>
            <!-- Map, Battery, Temperature -->
            <div class="col-md-7">
                <!-- Map -->
                <div class="card">
                    <div class="card-body">
                        <h3 class="header-title mb-2">Device Map</h3>
                        <div id="device_map" class="gmaps" style="height: 60vh;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}
{% block extra_javascript %}
    <!-- Third party js -->
    <script src="{% static 'js/vendor/Chart.bundle.min.js' %}"></script>
    <script src="{% static 'js/vendor/apexcharts.min.js' %}"></script>
    <script src="{% static 'js/custom/dashboards/common/device_map.js' %}"></script>
    <script src="https://maps.googleapis.com/maps/api/js?key={{ GOOGLE_MAPS_API_KEY }}&callback=initMap&loading=async"
            async
            defer></script>
    <!-- App js -->
    <script>
        const deviceIds = [{{device.id}}];
        const fieldIds = [{{ device.fild.id }}];
    </script>
    <script src="{% static 'js/custom/dashboards/common/battery_indicator_gauge.js' %}"></script>
    <script src="{% static 'js/custom/dashboards/common/thermometer.js' %}"></script>
    <script src="{% static 'js/custom/dashboards/common/events_list_generator.js' %}"></script>
    <script src="{% static 'js/custom/dashboards/common/time_elapsed.js' %}"></script>
    <script src="{% static 'js/custom/dashboards/common/fetch_fields.js' %}"></script>
    <script src="{% static 'js/custom/dashboards/whiskers-gateway-v1.js' %}"></script>
{% endblock extra_javascript %}
