{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
    Fields
{% endblock title %}
{% block content %}
    <!-- Start Content-->
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row mt-1">
            <div class="col-lg">
                <div class="page-title-box">
                    <h4 class="page-title">Fields</h4>
                </div>
            </div>
            <div class="col-lg-auto">
                <div class="modal-footer d-flex justify-content-center align-items-center px-0 mt-1">
                    <a href="{% url 'fields:create' %}"
                       class="btn btn-success rounded-pill mb-3 {% if not user.is_superuser %}disabled{% endif %}"><i class="mdi mdi-plus me-1"></i>Create Field</a>
                </div>
            </div>
        </div>
        <!-- end page title -->
        <!-- end row-->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table id="fieldsTable"
                           class="table table-centered table-nowrap order-column mb-0 hover">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Location</th>
                                <th>Area</th>
                                <th>Color</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for field in list %}
                                <tr>
                                    <td>{{ field.id }}</td>
                                    <td>{{ field.name }}</td>
                                    <td>{{ field.loca }}</td>
                                    <td>
                                        {{ field.covr }} km<sup>2</sup>
                                    </td>
                                    <td>
                                        <i class="mdi mdi-circle" style="color: {{ field.colr }};"></i> {{ field.colr }}
                                    </td>
                                    <!-- Action Buttons -->
                                    <td class="table-action">
                                        {% if user.is_superuser %}
                                            <a href="{% url 'fields:edit' field_id=field.id %}" class="action-icon"> <i class="mdi mdi-pencil"></i></a>
                                            <a href="#"
                                               data-toggle="modal"
                                               data-target="#deleteModal-{{ field.id }}"
                                               class="action-icon"> <i class="mdi mdi-delete"></i></a>
                                        {% else %}
                                            <a class="action-icon"> <i class="mdi mdi-pencil" style="opacity:0.5;"></i></a>
                                            <a class="action-icon"> <i class="mdi mdi-delete" style="opacity:0.5;"></i></a>
                                        {% endif %}
                                    </td>
                                    <!-- The Modal -->
                                    <div class="modal fade"
                                         id="deleteModal-{{ field.id }}"
                                         tabindex="-1"
                                         role="dialog"
                                         aria-labelledby="deleteModalLabel-{{ field.id }}"
                                         aria-hidden="true">
                                        <div class="modal-dialog modal-dialog-centered" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h4 class="modal-title" id="deleteModalLabel-{{ field.id }}">Delete Confirmation</h4>
                                                </div>
                                                <div class="modal-body">
                                                    Are you sure you want to delete <b>{{ field.name }}</b>? This action cannot be undone.
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                    <a href="{% url 'fields:delete' field.id %}" method="POST">
                                                        <button type="button" class="btn btn-danger">Delete</button>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    <!-- end row-->
                </div>
            </div>
        </div>
    </div>
    <!-- container -->
{% endblock content %}
{% block extra_javascript %}
    <!-- Datatables -->
    <script src="{% static 'js/vendor.min.js' %}"></script>
    <script src="{% static 'js/vendor/datatables.min.js' %}"></script>
    <script src="{% static 'js/custom/data_tables.js' %}"></script>
    <!-- Third party js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.6.1/font/bootstrap-icons.css"
          rel="stylesheet" />
    <!-- Third party js ends -->
    <!-- Init js -->
    <!-- Init js end -->
{% endblock extra_javascript %}
