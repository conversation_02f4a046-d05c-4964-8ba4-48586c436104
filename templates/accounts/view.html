{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
    User Profile
{% endblock title %}
{% block content %}
    <!-- Start Content-->
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <h4 class="page-title">
                        <a href="#" onclick="javascript:history.back()" class="link-secondary"><i class="dripicons-arrow-thin-left"></i></a> Profile View
                    </h4>
                </div>
            </div>
        </div>
        <!-- end page title -->
        <!-- Profile Start-->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title">User Profile</h4>
                        <p class="text-muted font-14">View user details on the system.</p>
                        <ul class="nav nav-tabs nav-bordered mb-2">
                        </ul>
                        <!-- end nav-->
                        <div class="row">
                            <div class="col-lg-3">
                                <div class="my-2 mt-3 text-center avatar-container">
                                    <img id="avatar-preview"
                                         src="{% if user.pict %}/media/{{ user.pict }}{% else %}{% static 'images/profile.png' %}{% endif %}"
                                         alt="Avatar"
                                         height="123px"
                                         width="123px"
                                         class="rounded-circle avatar">
                                </div>
                                <div class="mb-2">
                                    <label class="form-label">Profile Picture</label>
                                </div>
                            </div>
                            <div class="col-lg">
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="mb-2">
                                            <label class="form-label">First Name</label>
                                            <p>{{ user.fnam }}</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="mb-2">
                                            <label class="form-label">Last Name</label>
                                            <p>{{ user.lnam }}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="mb-2">
                                            <label class="form-label">Title</label>
                                            <p>{{ user.titl }}</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="mb-2">
                                            <label class="form-label">Organization</label>
                                            <p>{{ user.orgn }}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <label class="form-label">Phone</label>
                                    <p>+968 {{ user.phon }}</p>
                                </div>
                            </div>
                            <div class="col-lg">
                                <div class="row">
                                    <div class="col-lg">
                                        <div class="mb-2">
                                            <label class="form-label">Username</label>
                                            <p>{{ user.usrn }}</p>
                                        </div>
                                    </div>
                                    <div class="col-lg">
                                        <div class="mb-2">
                                            <label class="form-label">Role</label>
                                            <p>{{ user.role }}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <label class="form-label">Email</label>
                                    <p>{{ user.emal }}</p>
                                </div>
                            </div>
                        </div>
                        <hr class="solid">
                        <div class="row">
                            <div class="col-lg">
                                <div class="mb-2">
                                    <label class="form-label">Devices</label>
                                    <p>{{ user.devs }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- end row-->
                </div>
                <!-- end preview-->
            </div>
            <!-- end card-body -->
        </div>
        <!-- end card -->
    </div>
    <!-- end col -->
{% endblock content %}
{% block extra_javascript %}
{% endblock extra_javascript %}
