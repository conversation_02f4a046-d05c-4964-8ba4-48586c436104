{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
    Device Users
{% endblock title %}
{% block content %}
    <!-- Start Content-->
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row">
            <div class="col-lg">
                <div class="page-title-box">
                    <h2 class="page-title">Users</h2>
                </div>
            </div>
            <div class="col-lg-auto">
                <div class="modal-footer d-flex justify-content-center align-items-center px-0 mt-1">
                    <a href="{% url 'accounts:create' %}"
                       class="btn btn-success rounded-pill mb-3 {% if not user.is_superuser %}disabled{% endif %}"><i class="mdi mdi-plus me-1"></i>Create User</a>
                </div>
            </div>
        </div>
        <!-- end page title -->
        <!-- end row-->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table id="userProfilesTable"
                           class="table table-centered table-nowrap order-column mb-0 hover">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>Picture</th>
                                <th>First Name</th>
                                <th>Last Name</th>
                                <th>Organization</th>
                                <th>Role</th>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for profile in user_list %}
                                <tr>
                                    <td>{{ profile.user.id }}</td>
                                    <td>
                                        <div class="avatar-container">
                                            <img id="avatar-preview"
                                                 src="{% if profile.pict %}/media/{{ profile.pict }}{% else %}{% static 'images/profile.png' %}{% endif %}"
                                                 alt="Avatar"
                                                 height="25px"
                                                 width="25px"
                                                 class="rounded-circle avatar">
                                        </div>
                                    </td>
                                    <td>{{ profile.user.first_name }}</td>
                                    <td>{{ profile.user.last_name }}</td>
                                    <td>{{ profile.orgn }}</td>
                                    <td>{{ profile.role }}</td>
                                    <td>{{ profile.user.username }}</td>
                                    <td>{{ profile.user.email }}</td>
                                    <!-- Action Buttons -->
                                    <td class="table-action">
                                        {% if user.is_superuser %}
                                            <a href="{% url 'accounts:edit' profile_id=profile.id %}"
                                            class="action-icon"> <i class="mdi mdi-pencil"></i></a>
                                            <a href="#"
                                            data-toggle="modal"
                                            data-target="#deleteModal-{{ profile.id }}"
                                            class="action-icon"> <i class="mdi mdi-delete"></i></a>
                                            <a href="{% url 'notification_center:delivery_options' profile_id=profile.id %}"
                                            class="action-icon"> <i class="mdi mdi-send mdi-rotate-315"></i></a>
                                        {% else %}
                                            <a class="action-icon"> <i class="mdi mdi-pencil" style="opacity: 0.5;"></i></a>
                                            <a class="action-icon"> <i class="mdi mdi-delete" style="opacity: 0.5;"></i></a>
                                            <a class="action-icon"> <i class="mdi mdi-send mdi-rotate-315" style="opacity: 0.5;"></i></a>
                                        {% endif %}
                                    </td>
                                    <!-- The Modal -->
                                    <div class="modal fade"
                                         id="deleteModal-{{ profile.id }}"
                                         tabindex="-1"
                                         role="dialog"
                                         aria-labelledby="deleteModalLabel-{{ profile.id }}"
                                         aria-hidden="true">
                                        <div class="modal-dialog modal-dialog-centered" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h4 class="modal-title" id="deleteModalLabel-{{ profile.id }}">Delete Confirmation</h4>
                                                </div>
                                                <div class="modal-body">
                                                    Are you sure you want to delete the user <b>{{ profile.user.first_name }} {{ profile.user.last_name }}</b> with user ID <b>{{ profile.user.id }}</b>? This action cannot be undone.
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                    <a href="{% url 'accounts:delete' profile.id %}" method="POST">
                                                        <button type="button" class="btn btn-danger">Delete</button>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    <!-- end row-->
                </div>
            </div>
        </div>
    </div>
    <!-- container -->
{% endblock content %}
{% block extra_javascript %}
    <!-- Datatables -->
    <script src="{% static 'js/vendor.min.js' %}"></script>
    <script src="{% static 'js/vendor/datatables.min.js' %}"></script>
    <script src="{% static 'js/custom/data_tables.js' %}"></script>
    <!-- Third party js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.6.1/font/bootstrap-icons.css"
          rel="stylesheet" />
    <!-- Third party js ends -->
    <!-- Init js -->
    <!-- Init js end -->
{% endblock extra_javascript %}
