{% extends "auth_base.html" %}
{% load static %}
{% block title %}
    First Admin Setup Form
{% endblock title %}
{% block content %}
    <!-- Start Content-->
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <h4 class="page-title">Admin Setup</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->
        <!-- Form Start-->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        {% if form.errors %}
                            <div class="alert alert-danger">
                                <strong>Form Error!</strong> Please fix the following:
                                <ul class="mb-0">
                                    {% for field in form %}
                                        {% if field.errors %}<li>{{ field.label }}: {{ field.errors|striptags }}</li>{% endif %}
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}
                        <h4 class="header-title">User Profile</h4>
                        <p class="text-muted font-14">Create the first Admin user, this form cannot be accessed again once created.</p>
                        <ul class="nav nav-tabs nav-bordered mb-2">
                        </ul>
                        <!-- end nav-->
                        <form action=""
                              method="post"
                              autocomplete="off"
                              enctype="multipart/form-data">
                            {% csrf_token %}
                            <div class="row">
                                <div class="col-lg-3">
                                    <div class="my-2 mt-3 text-center avatar-container">
                                        <img id="avatar-preview"
                                             src="{% if form.pict.value %}/media/{{ form.pict.value }}{% else %}{% static 'images/profile.png' %}{% endif %}"
                                             alt="Avatar"
                                             height="123px"
                                             width="123px"
                                             class="rounded-circle avatar">
                                    </div>
                                    <div class="mb-2">
                                        <label for="{{ form.pict.id_for_label }}" class="form-label">{{ form.pict.label }}</label>
                                        {{ form.pict }}
                                    </div>
                                </div>
                                <div class="col-lg">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="mb-2">
                                                <label for="{{ form.fnam.id_for_label }}" class="form-label">{{ form.fnam.label }}</label>
                                                {{ form.fnam }}
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="mb-2">
                                                <label for="{{ form.lnam.id_for_label }}" class="form-label">{{ form.lnam.label }}</label>
                                                {{ form.lnam }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="mb-2">
                                                <label for="{{ form.titl.id_for_label }}" class="form-label">{{ form.titl.label }}</label>
                                                {{ form.titl }}
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="mb-2">
                                                <label for="{{ form.orgn.id_for_label }}" class="form-label">{{ form.orgn.label }}</label>
                                                {{ form.orgn }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <label for="{{ form.phon.id_for_label }}" class="form-label">{{ form.phon.label }}</label>
                                        <div class="input-group flex-nowrap">
                                            <span class="input-group-text" id="basic-addon1">+968</span>
                                            {{ form.phon }}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg">
                                    <div class="row">
                                        <div class="col-lg">
                                            <div class="mb-2">
                                                <label for="{{ form.usrn.id_for_label }}" class="form-label">{{ form.usrn.label }}</label>
                                                {{ form.usrn }}
                                            </div>
                                        </div>
                                        <div class="col-lg">
                                            <div class="mb-2">
                                                <label for="{{ form.role.id_for_label }}" class="form-label">{{ form.role.label }}</label>
                                                {{ form.role }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <label for="{{ form.emal.id_for_label }}" class="form-label">{{ form.emal.label }}</label>
                                        {{ form.emal }}
                                    </div>
                                    <div class="mb-2">
                                        <label for="{{ form.pwrd.id_for_label }}" class="form-label">{{ form.pwrd.label }}</label>
                                        <div class="input-group input-group-merge">
                                            {{ form.pwrd }}
                                            <div class="input-group-text" data-password="false">
                                                <span class="password-eye"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary float-end mt-2">Save</button>
                            </div>
                        </form>
                    </div>
                    <!-- end row-->
                </div>
                <!-- end preview-->
            </div>
            <!-- end card-body -->
        </div>
        <!-- end card -->
    </div>
    <!-- end col -->
</div>
<!-- end row -->
</div>
<!-- container -->
{% endblock content %}
{% block extra_javascript %}
    <script>
    // Get the file input element
    var fileInput = document.getElementById("{{ form.pict.auto_id }}");

    // Add an event listener to handle the file input change event
    fileInput.addEventListener("change", function (event) {
        var file = event.target.files[0]; // Get the selected file

        if (file) {
            // Create a FileReader object to read the file
            var reader = new FileReader();

            // Set the callback function to execute when the file is successfully read
            reader.onload = function (e) {
                // Update the preview image source with the newly selected image
                document.getElementById("avatar-preview").src = e.target.result;
            };

            // Read the file as a data URL (base64 format)
            reader.readAsDataURL(file);
        }
    });
    </script>
{% endblock extra_javascript %}
