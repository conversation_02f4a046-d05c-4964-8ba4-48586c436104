{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
    Settings
{% endblock title %}
{% block content %}
    <!-- Start Content-->
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <h4 class="page-title">Settings</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->
        <div class="card">
            <div class="card-body">
                {% if messages %}
                    <ul class="messages px-0">
                        {% for message in messages %}
                            <div class="alert {% if message.tags %}{% if 'error' in message.tags %}alert-danger{% else %}alert-{{ message.tags }}{% endif %}{% endif %}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    </ul>
                {% endif %}
                {% if email_form.errors %}
                    <div class="alert alert-danger">
                        <strong>Form Error!</strong> Please fix the following:
                        <ul class="mb-0">
                            {% for field in form %}
                                {% if field.errors %}<li>{{ field.label }}: {{ field.errors|striptags }}</li>{% endif %}
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}
                {% if sms_form.errors %}
                    <div class="alert alert-danger">
                        <strong>Form Error!</strong> Please fix the following:
                        <ul class="mb-0">
                            {% for field in form %}
                                {% if field.errors %}<li>{{ field.label }}: {{ field.errors|striptags }}</li>{% endif %}
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}
                <div class="row">
                    <div class="col-sm-3 mb-2 mb-sm-0">
                        <div class="nav flex-column nav-pills"
                             id="v-pills-tab"
                             role="tablist"
                             aria-orientation="vertical">
                            <a class="nav-link active show"
                               id="v-pills-email-tab"
                               data-bs-toggle="pill"
                               href="#v-pills-email"
                               role="tab"
                               aria-controls="v-pills-email"
                               aria-selected="true">
                                <div class="row">
                                    <div class="col-auto">
                                        <i class="mdi mdi-email-multiple d-block"></i>
                                    </div>
                                    <div class="col">
                                        <span class="d-none d-md-block">SMTP Email</span>
                                    </div>
                                </div>
                            </a>
                            <div style="height:10px;"></div>
                            <a class="nav-link show"
                               id="v-pills-sms-tab"
                               data-bs-toggle="pill"
                               href="#v-pills-sms"
                               role="tab"
                               aria-controls="v-pills-sms"
                               aria-selected="false">
                                <div class="row">
                                    <div class="col-auto">
                                        <i class="mdi mdi-message d-block"></i>
                                    </div>
                                    <div class="col">
                                        <span class="d-none d-md-block">SMS</span>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                    <!-- end col-->
                    <div class="col-sm-9">
                        <div class="tab-content" id="v-pills-tabContent">
                            <!-- SMTP Email Tab -->
                            <div class="tab-pane fade active show"
                                 id="v-pills-email"
                                 role="tabpanel"
                                 aria-labelledby="v-pills-email-tab">
                                <form action="" method="post" autocomplete="off" id="sms-settings-form">
                                    {% csrf_token %}
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="header-title">SMTP Email Setup</h4>
                                            <p class="text-muted font-14">Configure the email server to be used by the backend to send notifications.</p>
                                            <ul class="nav nav-tabs nav-bordered mb-2">
                                            </ul>
                                            <!-- end nav-->
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <div class="mb-2">
                                                        <label for="{{ email_form.host.id_for_label }}" class="form-label">{{ email_form.host.label }}</label>
                                                        {{ email_form.host }}
                                                    </div>
                                                    <div class="row">
                                                        <div class="col">
                                                            <div class="mb-2">
                                                                <label for="{{ email_form.port.id_for_label }}" class="form-label">{{ email_form.port.label }}</label>
                                                                {{ email_form.port }}
                                                            </div>
                                                        </div>
                                                        <div class="col-auto">
                                                            <label class="mb-2">TLS/SSL</label>
                                                            <div class="checkbox-container">
                                                                {{ email_form.tssl }}
                                                                <label for="{{ email_form.tssl.id_for_label }}"
                                                                       data-on-label="ON"
                                                                       data-off-label="OFF"></label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-6">
                                                    <div class="mb-2">
                                                        <label for="{{ email_form.usrn.id_for_label }}" class="form-label">{{ email_form.usrn.label }}</label>
                                                        {{ email_form.usrn }}
                                                    </div>
                                                    <div class="mb-2">
                                                        <label for="{{ email_form.pwrd.id_for_label }}" class="form-label">{{ email_form.pwrd.label }}</label>
                                                        <div class="input-group input-group-merge">
                                                            {{ email_form.pwrd }}
                                                            <div class="input-group-text" data-password="false">
                                                                <span class="password-eye"></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <button type="submit"
                                                        class="btn btn-primary mt-2"
                                                        id="save-button-1"
                                                        name="email_form">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <!-- SMS Tab -->
                            <div class="tab-pane fade"
                                 id="v-pills-sms"
                                 role="tabpanel"
                                 aria-labelledby="v-pills-sms-tab">
                                <form action="" method="post" autocomplete="off" id="sms-settings-form">
                                    {% csrf_token %}
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="header-title">SMS Setup</h4>
                                            <p class="text-muted font-14">Configure the email server to be used by the backend to send notifications.</p>
                                            <ul class="nav nav-tabs nav-bordered mb-2">
                                            </ul>
                                            <!-- end nav-->
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <div class="mb-2">
                                                        <label for="{{ sms_form.asid.id_for_label }}" class="form-label">{{ sms_form.asid.label }}</label>
                                                        {{ sms_form.asid }}
                                                    </div>
                                                </div>
                                                <div class="col-lg-6">
                                                    <div class="mb-2">
                                                        <label for="{{ sms_form.phon.id_for_label }}" class="form-label">{{ sms_form.phon.label }}</label>
                                                        {{ sms_form.phon }}
                                                    </div>
                                                </div>
                                                <div class="mb-2">
                                                    <label for="{{ sms_form.tokn.id_for_label }}" class="form-label">{{ sms_form.tokn.label }}</label>
                                                    <div class="input-group input-group-merge">
                                                        {{ sms_form.tokn }}
                                                        <div class="input-group-text" data-password="false">
                                                            <span class="password-eye"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <button type="submit"
                                                        class="btn btn-primary mt-2"
                                                        id="save-button-2"
                                                        name="sms_form">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <!-- end tab-content-->
                        <!-- end col-->
                    </form>
                </div>
                <!-- end row-->
            </div>
            <!-- end row-->
        </div>
    </div>
    <!-- end col -->
{% endblock content %}
