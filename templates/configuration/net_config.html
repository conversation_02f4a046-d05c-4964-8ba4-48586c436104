{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
    Network Configuration
{% endblock title %}
{% block content %}
    <!-- Start Content-->
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <h4 class="page-title">
                        <a href="#" onclick="javascript:history.back()" class="link-secondary"><i class="dripicons-arrow-thin-left"></i></a> Network Configuration
                    </h4>
                </div>
            </div>
        </div>
        <!-- end page title -->
        <!-- Form Start-->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        {% if messages %}
                            <ul class="messages px-0">
                                {% for message in messages %}
                                    <div class="alert {% if message.tags %}{% if 'error' in message.tags %}alert-danger{% else %}alert-{{ message.tags }}{% endif %}{% endif %}">
                                        {{ message }}
                                    </div>
                                {% endfor %}
                            </ul>
                        {% endif %}
                        {% if form.errors %}
                            <div class="alert alert-danger">
                                <strong>Form Error!</strong> Please fix the following:
                                <ul class="mb-0">
                                    {% for field in form %}
                                        {% if field.errors %}<li>{{ field.label }}: {{ field.errors|striptags }}</li>{% endif %}
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}
                        <div class="avatar-sm float-end">
                            {% if is_synchronized %}
                                <span class="avatar-title bg-success-lighten rounded-circle h3 my-0">
                                    <i class="mdi mdi-check-bold text-success"
                                       data-bs-toggle="tooltip"
                                       data-bs-title="Synchronized"></i>
                                </span>
                            {% else %}
                                <span class="avatar-title bg-warning-lighten rounded-circle h3 my-0">
                                    <i class="mdi mdi-sync text-warning"
                                       data-bs-toggle="tooltip"
                                       data-bs-title="Unsynchronized"></i>
                                </span>
                            {% endif %}
                        </div>
                        <h4 class="header-title">Network Configuration</h4>
                        <p class="text-muted font-14">Use this form to configure the Whiskers Network.</p>
                        <ul class="nav nav-tabs nav-bordered mb-3">
                        </ul>
                        <!-- end nav-->
                        <form action="" method="post" autocomplete="off" id="network-form">
                            {% csrf_token %}
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="mb-2">
                                        {% if synchronization_status.txpw %}
                                            <span class="badge badge-success-lighten"><i class="mdi mdi-check-bold text-success"></i></span>
                                        {% else %}
                                            <span class="badge badge-warning-lighten"><i class="mdi mdi-sync text-warning"></i></span>
                                        {% endif %}
                                        <label for="{{ form.txpw.id_for_label }}" class="form-label">{{ form.txpw.label }}</label>
                                        <div class="container" style="overflow: hidden;">{{ form.txpw }}</div>
                                        <small class="form-text text-muted">Set the maximum transmit power of the device in dB (RF Output Power)</small>
                                    </div>
                                    <div class="mb-2 mt-1">
                                        {% if synchronization_status.duty %}
                                            <span class="badge badge-success-lighten"><i class="mdi mdi-check-bold text-success"></i></span>
                                        {% else %}
                                            <span class="badge badge-warning-lighten"><i class="mdi mdi-sync text-warning"></i></span>
                                        {% endif %}
                                        <label for="{{ form.duty.id_for_label }}" class="form-label">{{ form.duty.label }}</label>
                                        <div class="container" style="overflow: hidden;">{{ form.duty }}</div>
                                        <small class="form-text text-muted">Indicates the minimum amount of time required before the next transmission.</small>
                                    </div>
                                    <div class="mb-2 mt-1">
                                        {% if synchronization_status.retx %}
                                            <span class="badge badge-success-lighten"><i class="mdi mdi-check-bold text-success"></i></span>
                                        {% else %}
                                            <span class="badge badge-warning-lighten"><i class="mdi mdi-sync text-warning"></i></span>
                                        {% endif %}
                                        <label for="{{ form.retx.id_for_label }}" class="form-label">{{ form.retx.label }}</label>
                                        <div class="container" style="overflow: hidden;">{{ form.retx }}</div>
                                        <small class="form-text text-muted">How many times can the device retransmit the same message.</small>
                                    </div>
                                    <div class="mb-2 mt-1">
                                        {% if synchronization_status.pram %}
                                            <span class="badge badge-success-lighten"><i class="mdi mdi-check-bold text-success"></i></span>
                                        {% else %}
                                            <span class="badge badge-warning-lighten"><i class="mdi mdi-sync text-warning"></i></span>
                                        {% endif %}
                                        <label for="{{ form.pram.id_for_label }}" class="form-label">{{ form.pram.label }}</label>
                                        <div class="container" style="overflow: hidden;">{{ form.pram }}</div>
                                        <small class="form-text text-muted">Number of preamble bytes transmitted before the actual packet.</small>
                                    </div>
                                    <div class="row">
                                        <div class="col-lg-3">
                                            <div class="mb-2 mt-1">
                                                {% if synchronization_status.lbtk %}
                                                    <span class="badge badge-success-lighten"><i class="mdi mdi-check-bold text-success"></i></span>
                                                {% else %}
                                                    <span class="badge badge-warning-lighten"><i class="mdi mdi-sync text-warning"></i></span>
                                                {% endif %}
                                                <label for="{{ form.lbtk.id_for_label }}" class="mb-2">{{ form.lbtk.label }}</label>
                                                <div class="checkbox-container px-3">
                                                    {{ form.lbtk }}
                                                    <label for="{{ form.lbtk.id_for_label }}"
                                                           data-on-label="Yes"
                                                           data-off-label="No"></label>
                                                </div>
                                                <small class="form-text text-muted">Toggle listen before talk.</small>
                                            </div>
                                        </div>
                                        <div class="col-lg-5">
                                            <div class="mb-2 mt-1">
                                                {% if synchronization_status.rwdl %}
                                                    <span class="badge badge-success-lighten"><i class="mdi mdi-check-bold text-success"></i></span>
                                                {% else %}
                                                    <span class="badge badge-warning-lighten"><i class="mdi mdi-sync text-warning"></i></span>
                                                {% endif %}
                                                <label for="{{ form.rwdl.id_for_label }}" class="form-label">{{ form.rwdl.label }}</label>
                                                <div class="container" style="overflow: hidden;">{{ form.rwdl }}</div>
                                                <small class="form-text text-muted">Delay in seconds.</small>
                                            </div>
                                        </div>
                                        <div class="col-lg-4">
                                            <div class="mb-2 mt-1">
                                                {% if synchronization_status.rwln %}
                                                    <span class="badge badge-success-lighten"><i class="mdi mdi-check-bold text-success"></i></span>
                                                {% else %}
                                                    <span class="badge badge-warning-lighten"><i class="mdi mdi-sync text-warning"></i></span>
                                                {% endif %}
                                                <label for="{{ form.rwln.id_for_label }}" class="form-label">{{ form.rwln.label }}</label>
                                                <div class="container" style="overflow: hidden;">{{ form.rwln }}</div>
                                                <small class="form-text text-muted">Length in seconds.</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- end col -->
                                <div class="col-lg-6">
                                    {% if synchronization_status.cmsk %}
                                        <span class="badge badge-success-lighten"><i class="mdi mdi-check-bold text-success"></i></span>
                                    {% else %}
                                        <span class="badge badge-warning-lighten"><i class="mdi mdi-sync text-warning"></i></span>
                                    {% endif %}
                                    <label>Channel Config</label>
                                    <small class="form-text text-muted">Enable or disable a channel, and configure its frequency.</small>
                                    <div style="height: 500px; overflow-y: scroll;" class="mt-2">
                                        <div class="row">
                                            <div class="mb-2 mt-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="input-group flex-nowrap" style="overflow: hidden; flex: 1;">
                                                        <button class="btn" type="button" id="ch00_toggle">&nbsp0&nbsp</button>
                                                        {{ form.ch00_value }}<span class="input-group-text" id="basic-addon1">Hz</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="mb-2 mt-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="input-group flex-nowrap" style="overflow: hidden; flex: 1;">
                                                        <button class="btn" type="button" id="ch01_toggle">&nbsp1&nbsp</button>
                                                        {{ form.ch01_value }}<span class="input-group-text" id="basic-addon1">Hz</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="mb-2 mt-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="input-group flex-nowrap" style="overflow: hidden; flex: 1;">
                                                        <button class="btn" type="button" id="ch02_toggle">&nbsp2&nbsp</button>
                                                        {{ form.ch02_value }}<span class="input-group-text" id="basic-addon1">Hz</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="mb-2 mt-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="input-group flex-nowrap" style="overflow: hidden; flex: 1;">
                                                        <button class="btn" type="button" id="ch03_toggle">&nbsp3&nbsp</button>
                                                        {{ form.ch03_value }}<span class="input-group-text" id="basic-addon1">Hz</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="mb-2 mt-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="input-group flex-nowrap" style="overflow: hidden; flex: 1;">
                                                        <button class="btn" type="button" id="ch04_toggle">&nbsp4&nbsp</button>
                                                        {{ form.ch04_value }}<span class="input-group-text" id="basic-addon1">Hz</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="mb-2 mt-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="input-group flex-nowrap" style="overflow: hidden; flex: 1;">
                                                        <button class="btn" type="button" id="ch05_toggle">&nbsp5&nbsp</button>
                                                        {{ form.ch05_value }}<span class="input-group-text" id="basic-addon1">Hz</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="mb-2 mt-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="input-group flex-nowrap" style="overflow: hidden; flex: 1;">
                                                        <button class="btn" type="button" id="ch06_toggle">&nbsp6&nbsp</button>
                                                        {{ form.ch06_value }}<span class="input-group-text" id="basic-addon1">Hz</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="mb-2 mt-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="input-group flex-nowrap" style="overflow: hidden; flex: 1;">
                                                        <button class="btn" type="button" id="ch07_toggle">&nbsp7&nbsp</button>
                                                        {{ form.ch07_value }}<span class="input-group-text" id="basic-addon1">Hz</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="mb-2 mt-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="input-group flex-nowrap" style="overflow: hidden; flex: 1;">
                                                        <button class="btn" type="button" id="ch08_toggle">&nbsp8&nbsp</button>
                                                        {{ form.ch08_value }}<span class="input-group-text" id="basic-addon1">Hz</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="mb-2 mt-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="input-group flex-nowrap" style="overflow: hidden; flex: 1;">
                                                        <button class="btn" type="button" id="ch09_toggle">&nbsp9&nbsp</button>
                                                        {{ form.ch09_value }}<span class="input-group-text" id="basic-addon1">Hz</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="mb-2 mt-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="input-group flex-nowrap" style="overflow: hidden; flex: 1;">
                                                        <button class="btn" type="button" id="ch10_toggle">10</button>
                                                        {{ form.ch10_value }}<span class="input-group-text" id="basic-addon1">Hz</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="mb-2 mt-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="input-group flex-nowrap" style="overflow: hidden; flex: 1;">
                                                        <button class="btn" type="button" id="ch11_toggle">11</button>
                                                        {{ form.ch11_value }}<span class="input-group-text" id="basic-addon1">Hz</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="mb-2 mt-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="input-group flex-nowrap" style="overflow: hidden; flex: 1;">
                                                        <button class="btn" type="button" id="ch12_toggle">12</button>
                                                        {{ form.ch12_value }}<span class="input-group-text" id="basic-addon1">Hz</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="mb-2 mt-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="input-group flex-nowrap" style="overflow: hidden; flex: 1;">
                                                        <button class="btn" type="button" id="ch13_toggle">13</button>
                                                        {{ form.ch13_value }}<span class="input-group-text" id="basic-addon1">Hz</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="mb-2 mt-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="input-group flex-nowrap" style="overflow: hidden; flex: 1;">
                                                        <button class="btn" type="button" id="ch14_toggle">14</button>
                                                        {{ form.ch14_value }}<span class="input-group-text" id="basic-addon1">Hz</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="mb-2 mt-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="input-group flex-nowrap" style="overflow: hidden; flex: 1;">
                                                        <button class="btn" type="button" id="ch15_toggle">15</button>
                                                        {{ form.ch15_value }}<span class="input-group-text" id="basic-addon1">Hz</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Continue until 15 -->
                                    {{ form.ch00_enable }}
                                    {{ form.ch01_enable }}
                                    {{ form.ch02_enable }}
                                    {{ form.ch03_enable }}
                                    {{ form.ch04_enable }}
                                    {{ form.ch05_enable }}
                                    {{ form.ch06_enable }}
                                    {{ form.ch07_enable }}
                                    {{ form.ch08_enable }}
                                    {{ form.ch09_enable }}
                                    {{ form.ch10_enable }}
                                    {{ form.ch11_enable }}
                                    {{ form.ch12_enable }}
                                    {{ form.ch13_enable }}
                                    {{ form.ch14_enable }}
                                    {{ form.ch15_enable }}
                                    <!-- end col -->
                                </div>
                                <button type="submit" class="btn btn-primary float-end mt-2">Save</button>
                            </div>
                        </form>
                    </div>
                    <!-- end row-->
                </div>
                <!-- end preview-->
            </div>
            <!-- end card-body -->
        </div>
        <!-- end card -->
    </div>
    <!-- end col -->
</div>
<!-- end row -->
<!-- container -->
{% endblock content %}
{% block extra_javascript %}
    <!-- rSlider -->
    <link rel="stylesheet" href="{% static 'css/vendor/rSlider.min.css' %}">
    <script src="{% static 'js/vendor/rSlider.min.js' %}"></script>
    <script src="{% static 'js/custom/sliders.js' %}"></script>
    <!-- Code -->
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.2/codemirror.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.2/codemirror.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.62.2/mode/javascript/javascript.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // convert to an array of toggle and enable directly using document.getElementId
            const channelToggles = [
                [document.getElementById("ch00_toggle"), document.getElementById("id_ch00_enable")],
                [document.getElementById("ch01_toggle"), document.getElementById("id_ch01_enable")],
                [document.getElementById("ch02_toggle"), document.getElementById("id_ch02_enable")],
                [document.getElementById("ch03_toggle"), document.getElementById("id_ch03_enable")],
                [document.getElementById("ch04_toggle"), document.getElementById("id_ch04_enable")],
                [document.getElementById("ch05_toggle"), document.getElementById("id_ch05_enable")],
                [document.getElementById("ch06_toggle"), document.getElementById("id_ch06_enable")],
                [document.getElementById("ch07_toggle"), document.getElementById("id_ch07_enable")],
                [document.getElementById("ch08_toggle"), document.getElementById("id_ch08_enable")],
                [document.getElementById("ch09_toggle"), document.getElementById("id_ch09_enable")],
                [document.getElementById("ch10_toggle"), document.getElementById("id_ch10_enable")],
                [document.getElementById("ch11_toggle"), document.getElementById("id_ch11_enable")],
                [document.getElementById("ch12_toggle"), document.getElementById("id_ch12_enable")],
                [document.getElementById("ch13_toggle"), document.getElementById("id_ch13_enable")],
                [document.getElementById("ch14_toggle"), document.getElementById("id_ch14_enable")],
                [document.getElementById("ch15_toggle"), document.getElementById("id_ch15_enable")],    
            ];

            function updateButtonColor(channelEnable, channelToggle) {
                if (channelEnable.checked) {
                    channelToggle.classList.add("btn-primary");
                    channelToggle.classList.remove("btn-light");
                } else {
                    channelToggle.classList.remove("btn-primary");
                    channelToggle.classList.add("btn-light");
                }
            }

            for (let i = 0; i < channelToggles.length; i++) {
                const channelToggle = channelToggles[i][0];
                const channelEnable = channelToggles[i][1];
                // Set the initial button color
                updateButtonColor(channelEnable, channelToggle);
                channelToggle.addEventListener("click", function () {
                    // Toggle the value of channelEnable
                    channelEnable.checked = !channelEnable.checked;

                    // Update the button color after the toggle
                    updateButtonColor(channelEnable, channelToggle);
                });
            }
        });
    </script>
    <script>
        var duty_val_old = document.getElementById("id_duty").value;
        var txpw_val_old = document.getElementById("id_txpw").value;
        var pram_val_old = document.getElementById("id_pram").value;
        var retrans_val_old = document.getElementById("id_retx").value;
        var length_val_old = document.getElementById("id_rwln").value;
        var delay_val_old = document.getElementById("id_rwdl").value;

        var txpwSlider = new rSlider({
            target: 'id_txpw',
            values: { min: 0, max: 14 },
            range: false,
            tooltip: false,
            set: [parseInt(txpw_val_old)],
            step: 1,
            scale: true,
            labels: true,
        });

        var dutySlider = new rSlider({
            target: 'id_duty',
            values: { min: 0, max: 15 },
            range: false,
            tooltip: false,
            set: [parseInt(duty_val_old)],
            step: 1,
            scale: true,
            labels: true,
        });

        var pramSlider = new rSlider({
            target: 'id_pram',
            values: [0, 0.5, 1, 1.5, 2, 3, 4, 5, 6, 7, 8, 12, 24, 30],
            range: false,
            tooltip: false,
            set: [parseInt(pram_val_old)],
            scale: true,
            labels: true,
        });

        var pramSlider = new rSlider({
            target: 'id_retx',
            values: { min: 0, max: 15 },
            range: false,
            tooltip: false,
            set: [parseInt(retrans_val_old)],
            step: 1,
            scale: true,
            labels: true,
        });

        var lengthSlider = new rSlider({
            target: 'id_rwln',
            values: { min: 1, max: 6 },
            range: false,
            tooltip: false,
            set: [parseInt(length_val_old)],
            step: 1,
            scale: true,
            labels: true,
        });

        var delaySlider = new rSlider({
            target: 'id_rwdl',
            values: { min: 1, max: 6 },
            range: false,
            tooltip: false,
            set: [parseInt(delay_val_old)],
            step: 1,
            scale: true,
            labels: true,
        });
    </script>
{% endblock extra_javascript %}
