{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
    Whiskers Gateway V2 Configuration
{% endblock title %}
{% block content %}
    <!-- Start Content-->
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <h4 class="page-title">
                        <a href="#" onclick="javascript:history.back()" class="link-secondary"><i class="dripicons-arrow-thin-left"></i></a> {{ name }}
                    </h4>
                </div>
            </div>
        </div>
        <!-- end page title -->
        <!-- Form Start-->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        {% if messages %}
                            <ul class="messages px-0">
                                {% for message in messages %}
                                    <div class="alert {% if message.tags %}{% if 'error' in message.tags %}alert-danger{% else %}alert-{{ message.tags }}{% endif %}{% endif %}">
                                        {{ message }}
                                    </div>
                                {% endfor %}
                            </ul>
                        {% endif %}
                        {% if form.errors %}
                            <div class="alert alert-danger">
                                <strong>Form Error!</strong> Please fix the following:
                                <ul class="mb-0">
                                    {% for field in form %}
                                        {% if field.errors %}<li>{{ field.label }}: {{ field.errors|striptags }}</li>{% endif %}
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}
                        <div class="avatar-sm float-end">
                            {% if is_synchronized %}
                                <span class="avatar-title bg-success-lighten rounded-circle h3 my-0">
                                    <i class="mdi mdi-check-bold text-success"
                                       data-bs-toggle="tooltip"
                                       data-bs-title="Synchronized"></i>
                                </span>
                            {% else %}
                                <span class="avatar-title bg-warning-lighten rounded-circle h3 my-0">
                                    <i class="mdi mdi-sync text-warning"
                                       data-bs-toggle="tooltip"
                                       data-bs-title="Unsynchronized"></i>
                                </span>
                            {% endif %}
                        </div>
                        <h4 class="header-title">Whiskers Gateway V2 Configuration</h4>
                        <p class="text-muted font-14">Use this form to configure the gateway application settings.</p>
                        <ul class="nav nav-tabs nav-bordered mb-3">
                        </ul>
                        <!-- end nav-->
                        <form action="" method="post" autocomplete="off" id="network-form">
                            {% csrf_token %}
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="mb-2">
                                        {% if synchronization_status.upin %}
                                            <span class="badge badge-success-lighten"><i class="mdi mdi-check-bold text-success"></i></span>
                                        {% else %}
                                            <span class="badge badge-warning-lighten"><i class="mdi mdi-sync text-warning"></i></span>
                                        {% endif %}
                                        <label for="{{ form.upin.id_for_label }}" class="form-label">{{ form.upin.label }}</label>
                                        <div class="container" style="overflow: hidden;">{{ form.upin }}</div>
                                        <small class="form-text text-muted">Update interval of the device in hours.</small>
                                    </div>
                                    <div class="mb-2 mt-1">
                                        {% if synchronization_status.atim %}
                                            <span class="badge badge-success-lighten"><i class="mdi mdi-check-bold text-success"></i></span>
                                        {% else %}
                                            <span class="badge badge-warning-lighten"><i class="mdi mdi-sync text-warning"></i></span>
                                        {% endif %}
                                        <label for="{{ form.atim.id_for_label }}" class="form-label">{{ form.atim.label }}</label>
                                        <div class="container" style="overflow: hidden;">{{ form.atim }}</div>
                                        <small class="form-text text-muted">Indicates the minimum amount of time required before the next transmission.</small>
                                    </div>
                                    <div class="mb-2 mt-1">
                                        {% if synchronization_status.athr %}
                                            <span class="badge badge-success-lighten"><i class="mdi mdi-check-bold text-success"></i></span>
                                        {% else %}
                                            <span class="badge badge-warning-lighten"><i class="mdi mdi-sync text-warning"></i></span>
                                        {% endif %}
                                        <label for="{{ form.athr.id_for_label }}" class="form-label">{{ form.athr.label }}</label>
                                        <div class="container" style="overflow: hidden;">{{ form.athr }}</div>
                                        <small class="form-text text-muted">Number of preamble bytes transmitted before the actual packet.</small>
                                    </div>
                                    <div class="mb-2 mt-1">
                                        {% if synchronization_status.adet %}
                                            <span class="badge badge-success-lighten"><i class="mdi mdi-check-bold text-success"></i></span>
                                        {% else %}
                                            <span class="badge badge-warning-lighten"><i class="mdi mdi-sync text-warning"></i></span>
                                        {% endif %}
                                        <label for="{{ form.adet.id_for_label }}" class="mb-2">{{ form.adet.label }}</label>
                                        <div class="checkbox-container px-3">
                                            {{ form.adet }}
                                            <label for="{{ form.adet.id_for_label }}"
                                                   data-on-label="Yes"
                                                   data-off-label="No"></label>
                                        </div>
                                    </div>
                                </div>
                                <!-- end col -->
                                <div class="col-lg-6 d-flex align-items-center justify-content-center">
                                    <img src="{% static 'images/gateway_v2.png' %}"
                                         alt="Whiskers Gateway V2"
                                         width="300"
                                         style="transform: rotate(90deg)">
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary float-end mt-2">Save</button>
                    </div>
                </form>
            </div>
            <!-- end row-->
        </div>
        <!-- end preview-->
    </div>
    <!-- end card-body -->
</div>
<!-- end card -->
</div>
<!-- end col -->
</div>
<!-- end row -->
<!-- container -->
{% endblock content %}
{% block extra_javascript %}
    <!-- rSlider -->
    <link rel="stylesheet" href="{% static 'css/vendor/rSlider.min.css' %}">
    <script src="{% static 'js/vendor/rSlider.min.js' %}"></script>
    <script src="{% static 'js/custom/sliders.js' %}"></script>
    <!-- Code -->
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.2/codemirror.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.2/codemirror.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.62.2/mode/javascript/javascript.js"></script>
    <script>
        var upint_old_value = document.getElementById("id_upin").value;

        var txpwSlider = new rSlider({
            target: 'id_upin',
            values: { min: 1, max: 24 },
            range: false,
            tooltip: false,
            set: [parseInt(upint_old_value)],
            step: 1,
            scale: true,
            labels: true,
        });
    </script>
{% endblock extra_javascript %}
