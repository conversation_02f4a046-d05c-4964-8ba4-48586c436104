<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhiskersHub Alert</title>
    <style>
    body {
      font-family: Arial, Helvetica, sans-serif;
      background-color: #ffffff; /* Set background color to white */
      color: #495057;
      margin: 0;
      padding: 0;
    }

    .container {
      max-width: 600px;
      margin: 20px auto; /* Add margin for spacing */
      padding: 20px;
      text-align: center;
    }

    h1, h2, h3 {
      color: #617ebe;
    }

    p {
      margin-bottom: 20px;
      text-align: left;
    }

    a {
      color: #007bff;
      text-decoration: none;
    }

    .logo {
      max-width: 100%;
      height: auto;
      margin-bottom: 20px;
    }
    
    .text-center {
        text-align: center;
    }
    
    .footer {
      font-size: 12px;
      color: #9a9a9a; /* Faded color */
    }
    
    .rounded-card {
      border-radius: 10px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      padding: 10px 20px;
      background-color: #ffffff;
      text-align: left;
      width: 300px;
      margin: auto;
      text-decoration: none;
      color: inherit;
      display: block;
    }
    
    .Danger {
        color: #fa636e;
    }
    
    .Warning {
        color: #f9b957;
    }
    
    .Update {
        color: #4982fc;
    }

    .Info {
        color: #44c9ec;
    }
    </style>
</head>
<body>
  <div class="container">
      <img class="logo"
           src="https://static.wixstatic.com/media/63d0c2_c89aee513f61437abc28909e704814cb~mv2.jpg/v1/fill/w_212,h_53,al_c,q_80,usm_0.66_1.00_0.01,enc_auto/website%20logo%20coloured-09.jpg"
           alt="Logo">
      <h1 class="{{ notification_type }}">{{ notification_type }} Alert from {{ device_name }}</h1>
      <p>Greetings,</p>
      <p>
          The following alert has been sent from  {{ device_name }} at <a href="https://{{whiskers_hub_domain}}/">WhiskersHub</a>:
      </p>
      <a href="https://{{whiskers_hub_domain}}/{{ type }}/{{ device_id }}"
         class="rounded-card">
          <h3 class="{{ notification_type }}">{{ notification_type }} from Device {{device_name}}: </h3>
          <ul>
            {% for event in events %}
                <li>
                    {{ event.desc }}
                </li>
            {% endfor %}
        </ul>
        
      </a>
      <p>
        You're receiving this email because your notifications for this device are turned on. To configure your email preferences, please contact the Admin.
    </p>
      <p class="text-center">
          Thank you
          <br>
          The WhiskersHub Team
      </p>
      <p class="footer text-center">&copy; {{ year }} WhiskersHub. All rights reserved.</p>
  </div>
</body>