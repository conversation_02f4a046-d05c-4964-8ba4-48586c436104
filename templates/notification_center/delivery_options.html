{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
    Notification Settings
{% endblock title %}
{% block content %}
    <!-- Start Content-->
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <h4 class="page-title">
                        <a href="{% url 'accounts:list' %}" class="link-secondary"><i class="dripicons-arrow-thin-left"></i></a> Notification Settings
                    </h4>
                </div>
            </div>
        </div>
        <!-- end page title -->
        <!-- Form Start-->
        <div class="card">
            <div class="card-body">
                {% if messages %}
                    <ul class="messages px-0">
                        {% for message in messages %}
                            <div class="alert {% if message.tags %}{% if 'error' in message.tags %}alert-danger{% else %}alert-{{ message.tags }}{% endif %}{% endif %}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    </ul>
                {% endif %}
                {% if form.errors %}
                    <div class="alert alert-danger">
                        <strong>Form Error!</strong> Please fix the following:
                        <ul class="mb-0">
                            {% for field in form %}
                                {% if field.errors %}<li>{{ field.label }}: {{ field.errors|striptags }}</li>{% endif %}
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}
                <h4 class="header-title">Setup Delivery Options</h4>
                <p class="text-muted font-14">Configure which notifications are sent to the user's email account.</p>
                <ul class="nav nav-tabs nav-bordered mb-2">
                </ul>
                <!-- end nav-->
                <form action="" method="post" autocomplete="off" id="delivery-options-form">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-lg-6">
                            <label>Select Activities</label>
                            <p class="text-muted font-14">Select the activities that you'd like to be notified about.</p>
                            <div class="checkbox-container">
                                {{ form.rxif }}
                                <label for="{{ form.rxif.id_for_label }}"
                                       data-on-label="Yes"
                                       data-off-label="No"></label>
                                <span style="padding-left: 10px;">Info <i class="d-inline-block mdi mdi-information"
                                    tabindex="0"
                                    data-bs-toggle="tooltip"
                                    data-bs-title="Requests, invites, and other messages."></i>
                                </span>
                            </div>
                            <div class="checkbox-container">
                                {{ form.rxup }}
                                <label for="{{ form.rxup.id_for_label }}"
                                       data-on-label="Yes"
                                       data-off-label="No"></label>
                                <span style="padding-left: 10px;">Updates <i class="d-inline-block mdi mdi-information"
                                    tabindex="0"
                                    data-bs-toggle="tooltip"
                                    data-bs-title="Sensory, configuration, and status updates from devices."></i>
                                </span>
                            </div>
                            <div class="checkbox-container">
                                {{ form.rxwr }}
                                <label for="{{ form.rxwr.id_for_label }}"
                                       data-on-label="Yes"
                                       data-off-label="No"></label>
                                <span style="padding-left: 10px;">Warning <i class="d-inline-block mdi mdi-information"
                                    tabindex="0"
                                    data-bs-toggle="tooltip"
                                    data-bs-title="Low battery, status changes, fields exits..."></i>
                                </span>
                            </div>
                            <div class="checkbox-container">
                                {{ form.rxdg }}
                                <label for="{{ form.rxdg.id_for_label }}"
                                       data-on-label="Yes"
                                       data-off-label="No"></label>
                                <span style="padding-left: 10px;">Danger <i class="d-inline-block mdi mdi-information"
                                    tabindex="0"
                                    data-bs-toggle="tooltip"
                                    data-bs-title="Devices getting triggered, going offline, or malfunctioning."></i>
                                </span>
                            </div>
                            <div class="mt-3">
                                <label for="{{ form.mthd.id_for_label }}" class="form-label">{{ form.mthd.label }}</label>
                                <p class="text-muted font-14">How would you like to receive the notifications?</p>
                                {{ form.mthd }}
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="mb-2">
                                <label for="{{ form.devs.id_for_label }}" class="form-label">{{ form.devs.label }}</label>
                                <p class="text-muted font-14">Select the devices you want to receive notifications from.</p>
                                {{ form.devs }}
                            </div>
                        </div>
                        <button type="submit"
                                class="btn btn-primary float-end mt-2"
                                id="save-button"
                                disabled>Save</button>
                    </div>
                </form>
            </div>
            <!-- end row-->
        </div>
        <!-- end preview-->
    </div>
    <!-- end card-body -->
{% endblock content %}
{% block extra_javascript %}
    <script src="{% static 'js/custom/enable_disable_save.js' %}"></script>
{% endblock extra_javascript %}
