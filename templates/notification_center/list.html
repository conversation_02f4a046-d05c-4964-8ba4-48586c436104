{% extends "horizontal_base.html" %}
{% load static %}
{% load webpush_notifications %}
{% block title %}
    Device Profiles
{% endblock title %}
{% block content %}
{% webpush_header %}
    <!-- Start Content-->
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row mt-1">
            <div class="col-lg">
                <div class="page-title-box d-flex align-items-center">
                    <h4 class="page-title">Notifications</h4>
                    <div class="form-group mb-0 ms-3">
                        <div class="input-group">
                            <select class="form-select" id="notification_type" onchange="location = this.value;">
                                <option value="/notification_center/list/" {% if request.path == '/notification_center/list/' %}selected{% endif %}>All</option>
                                <option value="/notification_center/list/read/" {% if request.path == '/notification_center/list/read/' %}selected{% endif %}>Read</option>
                                <option value="/notification_center/list/unread/" {% if request.path == '/notification_center/list/unread/' %}selected{% endif %}>Unread</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-auto">
                <div class="modal-footer d-flex justify-content-center align-items-center px-0 mt-1">
                    <button id="webpush-subscribe-button" class="btn rounded-pill mb-3" data-url="/webpush/save_information"></button>
                    <script>
                        const button = document.getElementById('webpush-subscribe-button');
                    
                        function updateButtonColor() {
                            if (button.textContent === 'Subscribe to Push Messaging') {
                                button.classList.add('btn-success');
                                button.classList.remove('btn-danger');
                            } else {
                                button.classList.remove('btn-success');
                                button.classList.add('btn-danger');
                            }
                        }
                    
                        // Observe changes to the button's text content
                        const observer = new MutationObserver(() => {
                            updateButtonColor();
                        });
                    
                        // Configure the observer to watch for changes to the text content
                        observer.observe(button, { childList: true, subtree: true });
                    
                        // Clean up the observer when the button is clicked (if necessary)
                        button.addEventListener('click', () => {
                            updateButtonColor(); // For immediate update
                            // Optional: Disconnect the observer after a click
                            // observer.disconnect();
                        });
                    </script>
                    <form action="{% url 'notification_center:export_csv' %}" method="get">
                        <input type="hidden" name="notification_status" value="{% if '/read/' in request.path %}read{% elif '/unread/' in request.path %}unread{% endif %}">
                        <button type="submit" class="btn rounded-pill mb-3 btn-secondary"><i class="mdi mdi-file-download-outline">Download CSV</i></button>
                    </form>                    
                </div>
            </div>
        </div>
        <!-- end page title -->
        <!-- end row-->
        {% csrf_token %}
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table id="notificationsTable"
                           class="table table-centered table-nowrap order-column mb-0 hover">
                        <thead class="table-light">
                            <tr>
                                <th>Time</th>
                                <th>Type</th>
                                <th>Device</th>
                                <th>Description</th>
                                <th>Method</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for notification in list %}
                                <tr data-read="{% if notification.read %}read{% else %}unread{% endif %}">
                                    <td>{{ notification.evnt.crat }}</td>
                                    <td class="{% if notification.evnt.type == 'Info' %} text-info {% elif notification.evnt.type == 'Update' %} text-primary {% elif notification.evnt.type == 'Warning' %} text-warning {% else %} text-danger {% endif %} ">
                                        {{ notification.evnt.type }}
                                    </td>
                                    <td>{{ notification.evnt.devi.name }}</td>
                                    <td>{{ notification.evnt.desc }}</td>
                                    <td>{{ notification.mthd }}</td>
                                    <!-- Action Buttons -->
                                    <td class="table-action">
                                        {% if not notification.read %}
                                            {% csrf_token %}
                                            <a href="#"
                                               id="markAsReadLink-{{ notification.id }}"
                                               method="POST"
                                               class="action-icon mark-as-read-link"
                                               tabindex="0"
                                               data-bs-toggle="tooltip"
                                               data-bs-title="Mark as read">
                                                <i class="mdi mdi-check-circle-outline"></i>
                                            </a>
                                        {% else %}
                                            <a class="action-icon"> <i class="mdi mdi-check-circle" style="opacity: 0.5;"></i></a>
                                        {% endif %}
                                        <a href="#"
                                           data-toggle="modal"
                                           data-target="#deleteModal-{{ notification.id }}"
                                           class="action-icon"> <i class="mdi mdi-delete"></i></a>
                                    </td>
                                    <!-- The Modal -->
                                    <div class="modal fade"
                                         id="deleteModal-{{ notification.id }}"
                                         tabindex="-1"
                                         role="dialog"
                                         aria-labelledby="deleteModalLabel-{{ notification.id }}"
                                         aria-hidden="true">
                                        <div class="modal-dialog modal-dialog-centered" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h4 class="modal-title" id="deleteModalLabel-{{ notification.id }}">Delete Confirmation</h4>
                                                </div>
                                                <div class="modal-body">Are you sure you want to delete this notification? This action cannot be undone.</div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                    <a href="{% url 'notification_center:delete' notification.id %}"
                                                       method="POST">
                                                        <button type="button" class="btn btn-danger">Delete</button>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    <!-- Pagination Controls -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <p class="mb-0">
                                Showing page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </p>
                        </div>
                        <nav>
                            <ul class="pagination pagination-rounded mb-0">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">&laquo; Prev</a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">&laquo; Prev</span>
                                    </li>
                                {% endif %}

                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active"><span class="page-link">{{ num }}</span></li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                                    {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next &raquo;</a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">Next &raquo;</span>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>

                    <!-- end row-->
                </div>
            </div>
        </div>
    </div>
    <!-- container -->
{% endblock content %}
{% block extra_javascript %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <!-- Datatables -->
    <script src="{% static 'js/vendor.min.js' %}"></script>
    <!-- Other -->
    <script src="{% static 'js/custom/notif_set_read_list.js' %}"></script>
    <!-- Third party js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.6.1/font/bootstrap-icons.css"
          rel="stylesheet" />
    <!-- Third party js ends -->
{% endblock extra_javascript %}