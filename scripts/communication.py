import logging
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.core.mail import EmailMessage, send_mail
from django.conf import settings
from twilio.rest import Client
from datetime import datetime


SMS_MAX_LENGTH = 160
logger = logging.getLogger("app")


# kept for future use
def get_event_style(evnt_type):
    if evnt_type in ["Update", "Info"]:
        return {
            "text_color": "text-primary",
            "icon_class": "mdi-update",
            "icon_color": "bg-primary-lighten text-primary",
        }
    elif evnt_type == "Warning":
        return {
            "text_color": "text-warning",
            "icon_class": "mdi-alert",
            "icon_color": "bg-warning-lighten text-warning",
        }
    else:
        return {
            "text_color": "text-danger",
            "icon_class": "mdi-vibrate",
            "icon_color": "bg-danger-lighten text-danger",
        }


def send_invitation(profile, password: str):
    # send email
    html_message = render_to_string(
        "email/invitation.html",
        {
            "first_name": profile.user.first_name,
            "username": profile.user.username,
            "password": password,
            "whiskers_hub_domain": settings.WHISKERS_HUB_DOMAIN,
            "year": datetime.now().year,
        },
    )
    plain_message = strip_tags(html_message)
    try:
        send_mail(
            "Invitation to WhiskersHub",
            plain_message,
            settings.EMAIL_HOST_USER,
            [profile.user.email],
            html_message=html_message,
        )

        logger.info("Sent invitation email to '" + profile.user.email + "'")
    except Exception as e:
        logger.error(
            "Failed to send invitation email to '" + profile.user.email + "': " + str(e)
        )

    # send sms
    if profile.phon != "":
        try:
            account_sid = settings.TWILIO_ACCOUNT_SID
            auth_token = settings.TWILIO_AUTH_TOKEN
            client = Client(account_sid, auth_token)

            message = client.messages.create(
                body="Hi "
                + profile.user.first_name
                + ", you have been invited to WhiskersHub. Your username is "
                + profile.user.username
                + " and your password is "
                + password
                + ".",
                from_=settings.TWILIO_PHONE_NUMBER,
                to="+968" + profile.phon,
            )

            logger.info("Sent invitation sms to '" + profile.phon + "'")
        except Exception as e:
            logger.error(
                "Failed to send invitation sms to '" + profile.phon + "': " + str(e)
            )


# to be modified later to be reused
def send_email_notification(event_type, device, recipients, events):
    try:
        html_message = render_to_string(
            "email/notification.html",
            {
                "notification_type": event_type,
                "device_name": device.name,
                "device_id": device.id,
                "events": events,
                "type": device._meta.object_name.lower(),
                "whiskers_hub_domain": settings.WHISKERS_HUB_DOMAIN,
                "year": datetime.now().year,
            },
        )

        plain_message = strip_tags(html_message)

        email = EmailMessage(
            subject=f"{event_type} from {device.name}",
            body=plain_message,
            from_email=settings.EMAIL_HOST_USER,
            to=[settings.EMAIL_PRIMARY_RECIPIENT],
            bcc=recipients,
        )

        email.content_subtype = "html"  # Set the content type to HTML
        email.body = html_message

        email.send()
        return True
    except Exception as e:
        logger.error(f"Failed to send email notification: {e}")
        return False


# to be modified later to be reused
def send_sms_notification(event_type, device, recipients, events):
    sms_message = compile_sms_summary(event_type, device, events)
    account_sid = settings.TWILIO_ACCOUNT_SID
    auth_token = settings.TWILIO_AUTH_TOKEN
    client = Client(account_sid, auth_token)

    try:
        for recipient in recipients:
            client.messages.create(
                body=sms_message,
                from_=settings.TWILIO_PHONE_NUMBER,
                to=f"+968{recipient}",
            )
        return True
    except Exception as e:
        logger.error(f"Failed to send SMS notification: {e}")
        return False


# to be modified later to be reused
def compile_sms_summary(event_type, device, events):
    """
    Creates a single SMS message summary from the list of events and device information,
    limiting the message to two events and appending '... and more' if there are more events.
    """
    base_message = f"{event_type} from {device.name}: "
    msg_suffix = "..."
    event_summaries = []
    current_message = base_message

    max_events = min(2, len(events))  # Limit to 2 events
    available_space = SMS_MAX_LENGTH - len(base_message) - len(msg_suffix)
    # check events that fits the available space
    for event in events[:max_events]:
        if len(", ".join(event_summaries)) + len(event.desc) + 2 <= available_space:
            event_summaries.append(event.desc)

    # if all messages were too long truncate first message
    if not event_summaries and events:
        first_event = events[0].desc
        truncated_event = first_event[:available_space] + msg_suffix
        event_summaries.append(truncated_event)

    # join messages
    current_message = base_message + ", ".join(event_summaries)

    if (
        len(events) > 2
        and not current_message.endswith(msg_suffix)
        and len(current_message) + len(msg_suffix) <= SMS_MAX_LENGTH
    ):
        current_message += msg_suffix

    # Ensure message doesn't exceed SMS length
    return current_message[:SMS_MAX_LENGTH]


def send_pass_change(profile, password: str):
    html_message = render_to_string(
        "email/pass_change.html",
        {
            "first_name": profile.user.first_name,
            "username": profile.user.username,
            "password": password,
            "whiskers_hub_domain": settings.WHISKERS_HUB_DOMAIN,
            "year": datetime.now().year,
        },
    )
    plain_message = strip_tags(html_message)

    # email
    try:
        send_mail(
            "Password Change Notification",
            plain_message,
            settings.EMAIL_HOST_USER,
            [profile.user.email],
            html_message=html_message,
        )

        logger.info("Sent password change email to '" + profile.user.email + "'")
    except Exception as e:
        logger.error(
            "Failed to send password change email to '"
            + profile.user.email
            + "': "
            + str(e)
        )

    # sms
    if profile.phon != "":
        try:
            account_sid = settings.TWILIO_ACCOUNT_SID
            auth_token = settings.TWILIO_AUTH_TOKEN
            client = Client(account_sid, auth_token)

            message = client.messages.create(
                body="Hi "
                + profile.user.first_name
                + ", your password at WhiskersHub has been changed. Your username is "
                + profile.user.username
                + " and your password is "
                + password
                + ".",
                from_=settings.TWILIO_PHONE_NUMBER,
                to="+968" + profile.phon,
            )

            logger.info("Sent password change sms to '" + profile.phon + "'")
        except Exception as e:
            logger.error(
                "Failed to send password change sms to '"
                + profile.phon
                + "': "
                + str(e)
            )
