import os
import sys
import django

django_project_path = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(django_project_path)

# Set up the Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "app.settings")

django.setup()


def check_offline():
    from django.utils import timezone
    from django.db.models import F
    from django.contrib.contenttypes.models import ContentType
    from django.db.models import Q
    from device_manager.models import Device
    from notification_center.models import Event

    import time

    while True:
        devices = Device.objects.all()

        # Create events for devices going offline
        for device in devices:
            if (
                device.lupd is not None
                and (timezone.now() - timezone.timedelta(minutes=device.offp))
                > device.lupd
                and device.stat != "Offline"
            ):
                device.stat = "Offline"
                device.save()

                Event(
                    devi=device,
                    type="Danger",
                    desc="has gone offline.",
                ).save()

        time.sleep(60)


check_offline()
