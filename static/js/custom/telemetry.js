var telemetryOptions = {
    series: [{
        data: []
    }],
    chart: {
        id: 'chart2',
        type: 'line',
        height: 580,
        toolbar: {
            autoSelected: 'pan',
            show: false
        }
    },
    colors: ['#3688fc'],
    stroke: {
        width: 3
    },
    dataLabels: {
        enabled: false,
    },
    fill: {
        opacity: 1,
    },
    markers: {
        size: 5
    },
    xaxis: {
        type: 'datetime'
    }
};

var timelineOptions = {
    series: [{
        data: []
    }],
    chart: {
        id: 'chart1',
        height: 130,
        type: 'area',
        brush: {
            target: 'chart2',
            enabled: true
        },
        selection: {
            enabled: true,
            xaxis: {
            }
        },
    },
    colors: ['#fa6791'],
    fill: {
        type: 'gradient',
        gradient: {
            opacityFrom: 0.91,
            opacityTo: 0.1,
        }
    },
    xaxis: {
        type: 'datetime',
        tooltip: {
            enabled: false
        }
    },
    yaxis: {
        tickAmount: 2
    }
};

// Fetch telemetry data using the provided kwargs
fetch(`/telemetry/get-entries/${device_id}/${key}`)
    .then(response => response.json())
    .then(data => {
        // Populate the data series with telemetry values and datetime
        telemetryOptions.series[0].data = data.map(item => ({
            x: new Date(item.datetime).getTime(),
            y: item.value
        }));

        if (data.length != 0) {
            telemetryOptions.series[0].name = data[0].key;
        }

        timelineOptions.series[0].data = data.map(item => ({
            x: new Date(item.datetime).getTime(),
            y: item.value
        }));

        var telemetryChart = new ApexCharts(document.querySelector("#telemetry"), telemetryOptions);
        telemetryChart.render();

        var timelineChart = new ApexCharts(document.querySelector("#timeline"), timelineOptions);
        timelineChart.render();
    })
    .catch(error => {
        console.error("Error fetching telemetry data:", error);
    });
