$(document).ready(function () {
    // Get the initial form data
    var initialFormData = $('form').serialize();

    // Check if the form values have changed
    function isFormChanged() {
        var currentFormData = $('form').serialize();
        return currentFormData !== initialFormData;
    }

    // Enable or disable the save button based on form changes
    function toggleSaveButton() {
        var saveButton = $('#save-button');
        if (isFormChanged()) {
            saveButton.prop('disabled', false);
        } else {
            saveButton.prop('disabled', true);
        }
    }

    // Attach event handler to form inputs
    $('form :input').on('input change', function () {
        toggleSaveButton();
    });

    // Trigger initial check
    toggleSaveButton();
});