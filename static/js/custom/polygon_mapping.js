let polygon;

async function getLocation() {
    return new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject);
    });
}

async function updatePolygon(polygon, colorInput, areaElement, locationElement, geocoder, cordElement) {
    if (!polygon) return;

    const polygonArray = polygon.getPath().getArray().map(latLng => ({
        lat: latLng.lat(),
        lng: latLng.lng(),
    }));
    const colorValue = colorInput.value;

    polygon.setOptions({
        fillColor: colorValue,
        strokeColor: colorValue,
    });

    const area = google.maps.geometry.spherical.computeArea(polygon.getPath());
    const areaKm2 = (area / 1000000).toFixed(2);
    areaElement.value = areaKm2;

    const bounds = new google.maps.LatLngBounds();
    polygon.getPath().forEach(latLng => bounds.extend(latLng));
    const center = bounds.getCenter();

    geocoder.geocode({ location: center }, (results, status) => {
        if (status === "OK") {
            locationElement.value = results[0] ? results[0].formatted_address : "Unknown location";
        } else {
            locationElement.value = "Geocoder failed: " + status;
        }
    });

    cordElement.value = JSON.stringify(polygonArray);
}

async function initMap() {
    const position = await getLocation();

    const map = new google.maps.Map(document.getElementById("polygon_drawer"), {
        center: { lat: position.coords.latitude, lng: position.coords.longitude },
        zoom: 8,
    });

    polygon = new google.maps.Polygon({
        editable: true,
        draggable: true,
    });

    const cordElement = document.getElementById("id_cord");
    const colorInput = document.getElementById("id_colr");
    const areaElement = document.getElementById("id_covr");
    const locationElement = document.getElementById("id_loca");
    const geocoder = new google.maps.Geocoder();

    try {
        polygonArray = JSON.parse(cordElement.value);
    } catch (e) {
        polygonArray = [];
    }

    if (Array.isArray(polygonArray) && polygonArray.length > 2) {
        polygon = new google.maps.Polygon({
            paths: polygonArray,
            editable: true,
            draggable: true,
            fillColor: colorInput.value,
            strokeColor: colorInput.value,
        });
        polygon.setMap(map);

        // Calculate the bounds of the polygon and get its center
        const bounds = new google.maps.LatLngBounds();
        polygon.getPath().forEach(latLng => bounds.extend(latLng));
        const center = bounds.getCenter();

        // Set the center of the map to the polygon's center
        map.setCenter(center);

        // Adjust the viewport to contain the polygon
        map.fitBounds(bounds);
    } else {
        // Set the center of the map to the user's current location
        map.setCenter({ lat: position.coords.latitude, lng: position.coords.longitude });

        // Set a default zoom level
        map.setZoom(8);
    }

    const drawingManager = new google.maps.drawing.DrawingManager({
        drawingMode: google.maps.drawing.OverlayType.POLYGON,
        drawingControl: true,
        drawingControlOptions: {
            position: google.maps.ControlPosition.TOP_CENTER,
            drawingModes: [google.maps.drawing.OverlayType.POLYGON],
        },
        polygonOptions: {
            editable: true,
            draggable: true,
        },
    });
    drawingManager.setMap(map);

    colorInput.addEventListener("input", () => {
        updatePolygon(polygon, colorInput, areaElement, locationElement, geocoder, cordElement);
    });

    google.maps.event.addListener(polygon.getPath(), "set_at", () => {
        updatePolygon(polygon, colorInput, areaElement, locationElement, geocoder, cordElement);
    });

    google.maps.event.addListener(drawingManager, "polygoncomplete", newPolygon => {
        if (polygon) {
            polygon.setMap(null);
        }

        polygon = newPolygon;
        updatePolygon(polygon, colorInput, areaElement, locationElement, geocoder, cordElement);
    });
}
