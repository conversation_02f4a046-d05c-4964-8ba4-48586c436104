document.addEventListener('DOMContentLoaded', function() {
    
    
    const workShiftContainer = document.getElementById('work-shifts-container');

    // get week days as options
    const generateDayOptions = () => {
        return daysOfWeek.map(day => 
            `<option value="${day}">${day.charAt(0).toUpperCase() + day.slice(1)}</option>`
        ).join('');
    };

    // create a new shift element
    const createShiftElement = () => {
        const newShift = document.createElement('div');
        newShift.className = 'work-shift row gy-2 gx-2 align-items-center';
        
        newShift.innerHTML = `
            <div class="col-lg">
                <select name="day" class="form-select">
                    ${generateDayOptions()}
                </select>
            </div>
            <div class="col-lg">
                <input class="form-control" type="time" name="start_time" required>
            </div>
            <div class="col-auto mt-3">
                <p> - </p>
            </div>
            <div class="col-lg">
                <input class="form-control" type="time" name="end_time" required>
            </div>
            <div class="col-auto">
                <i class="mdi mdi-delete me-2 remove-shift" style="font-size: 20px;"></i>
            </div>
        `;

        // Event listener for the remove button
        newShift.querySelector('.remove-shift').addEventListener('click', () => {
            newShift.remove();
        });

        return newShift;
    };

    // Add shift button event listener
    document.querySelector('.add-shift').addEventListener('click', () => {
        const newShift = createShiftElement();
        workShiftContainer.appendChild(newShift);
    });

    // event listeners for existing remove shift buttons
    document.querySelectorAll('.remove-shift').forEach(button => {
        button.addEventListener('click', () => {
            button.closest('.work-shift').remove();
        });
    });

    // Check if a shift for a specific day and time exists
    const shiftExists = (day, startTime, endTime) => {
        const existingShifts = document.querySelectorAll('.work-shift');
        return Array.from(existingShifts).some(shift => {
            const shiftDay = shift.querySelector('select[name="day"]').value;
            const shiftStartTime = shift.querySelector('input[name="start_time"]').value;
            const shiftEndTime = shift.querySelector('input[name="end_time"]').value;

            return shiftDay === day && shiftStartTime === startTime && shiftEndTime === endTime;
        });
    };

    // Apply all unique shifts to the entire week
    document.querySelector('.apply-to-all').addEventListener('click', function() {
        const shiftsContainer = document.getElementById('work-shifts-container');
        const shifts = shiftsContainer.querySelectorAll('.work-shift');
        
        // Collect all unique shifts from all days
        const uniqueShifts = [];

        shifts.forEach(shift => {
            const shiftDay = shift.querySelector('select[name="day"]').value;
            const startTime = shift.querySelector('input[name="start_time"]').value;
            const endTime = shift.querySelector('input[name="end_time"]').value;

            // Store unique shifts with their time ranges
            uniqueShifts.push({
                day: shiftDay,
                startTime: startTime,
                endTime: endTime
            });
        });

        // Clone and apply these unique shifts to all other days without duplicates
        daysOfWeek.forEach(day => {
            uniqueShifts.forEach(shift => {
                if (shift.day !== day) {  // Ensure we aren't duplicating for the same day
                    // Only add the shift if it doesn't already exist for the day
                    if (!shiftExists(day, shift.startTime, shift.endTime)) {
                        const newShift = createShiftElement();
                        // Set the day, start time, and end time for the new shift
                        newShift.querySelector('select[name="day"]').value = day;
                        newShift.querySelector('input[name="start_time"]').value = shift.startTime;
                        newShift.querySelector('input[name="end_time"]').value = shift.endTime;

                        shiftsContainer.appendChild(newShift);
                    }
                }
            });
        });
    });
});
