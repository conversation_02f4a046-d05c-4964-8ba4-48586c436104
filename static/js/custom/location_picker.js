async function getLocation() {
    const position = new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject);
    });
    return position;
}

var fieldChanged = false;

async function initMap() {
    var idLati = parseFloat(document.getElementById('id_lati').value);
    var idLong = parseFloat(document.getElementById('id_long').value);
    var position;

    if (idLati === 0 && idLong === 0) {
        position = await getLocation();
    } else {
        position = {
            coords: {
                latitude: idLati,
                longitude: idLong
            }
        };
    }

    var fieldId = document.getElementById('id_fild').value;

    // Request needed libraries.
    //@ts-ignore
    const { Map } = await google.maps.importLibrary("maps");
    const { AdvancedMarkerElement } = await google.maps.importLibrary("marker");

    var map = new Map(document.getElementById('location_picker'), {
        center: { lat: position.coords.latitude, lng: position.coords.longitude },
        zoom: 8,
        mapId: "CREATE"
    });

    var marker = new AdvancedMarkerElement({
        position: { lat: position.coords.latitude, lng: position.coords.longitude },
        map: map,
        gmpDraggable: true
    });

    function updateMarkerAndForm(latitude, longitude) {
        marker.position = { lat: latitude, lng: longitude };
        document.getElementById('id_lati').value = parseFloat(latitude.toFixed(6));
        document.getElementById('id_long').value = parseFloat(longitude.toFixed(6));
    }

    updateMarkerAndForm(position.coords.latitude, position.coords.longitude);

    google.maps.event.addListener(marker, 'dragend', function (event) {
        updateMarkerAndForm(event.latLng.lat(), event.latLng.lng());
    });

    google.maps.event.addListener(map, 'click', function (event) {
        updateMarkerAndForm(event.latLng.lat(), event.latLng.lng());
    });

    document.getElementById('id_lati').addEventListener('input', function () {
        var latitude = parseFloat(this.value);
        var longitude = parseFloat(document.getElementById('id_long').value);
        updateMarkerAndForm(latitude, longitude);
        map.setCenter({ lat: latitude, lng: longitude });
    });

    document.getElementById('id_long').addEventListener('input', function () {
        var latitude = parseFloat(document.getElementById('id_lati').value);
        var longitude = parseFloat(this.value);
        updateMarkerAndForm(latitude, longitude);
        map.setCenter({ lat: latitude, lng: longitude });
    });

    // Store references to the polygons
    var polygons = [];

    renderField(fieldId);

    // Listen to field selector value changes
    var fieldSelector = document.getElementById('id_fild');
    fieldSelector.addEventListener('change', function () {
        fieldChanged = true;
        fieldId = this.value;
        if (fieldId) {
            // Remove previous polygons from the map
            polygons.forEach(function (polygon) {
                polygon.setMap(null);
            });
            polygons = [];

            renderField(fieldId);
        }
    });

    function renderField(fieldId) {
        // Make a request to fetch the fields data from the backend
        fetch('/fields/get/' + fieldId)
            .then(response => response.json())
            .then(field => {
                // Render the polygons for the selected field
                var polygon = new google.maps.Polygon({
                    paths: field.cord,
                    strokeColor: field.colr,
                    strokeOpacity: 0.8,
                    strokeWeight: 2,
                    fillColor: field.colr,
                    fillOpacity: 0.35,
                    map: map
                });
                polygons.push(polygon);

                // Move the marker to the center of the polygon
                var bounds = new google.maps.LatLngBounds();
                field.cord.forEach(function (coord) {
                    bounds.extend(coord);
                });
                map.fitBounds(bounds);
                if (fieldChanged)
                    marker.position = bounds.getCenter();

                // Update the input values with the new center coordinates
                var center = bounds.getCenter();
                if (fieldChanged)
                    updateMarkerAndForm(center.lat(), center.lng());
            })
            .catch(error => {
                console.error('Error:', error);
            });
    }

}