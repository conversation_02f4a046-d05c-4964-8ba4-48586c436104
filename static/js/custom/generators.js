$(document).ready(function () {
    $('#generate-eui').click(function () {
        var key = generateEUI();
        $(this).closest('.input-group').find('input').val(key);
    });

    $('#generate-address').click(function () {
        var key = getAddress();
        $(this).closest('.input-group').find('input').val(key);
    });

    $('#generate-apik').click(function () {
        var key = generateAPIKey();
        $(this).closest('.input-group').find('input').val(key);
    });
});

function generateAPIKey() {
    var key = '';
    var chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';

    // select random character from chars and add to key 16 times
    for (var i = 0; i < 16; i++) {
        key += chars[Math.floor(Math.random() * 62)];
    }
    return key;
}

function generateEUI() {
    var key = '';
    var chars = '0123456789ABCDEF';

    for (var i = 0; i < 16; i++) {
        key += chars[Math.floor(Math.random() * 16)];
    }

    // insert " • " every 2 characters
    key = key.match(/.{1,2}/g).join('  •  ');

    return key;
}

// get address from /device/get_address as json response from "address" and return it
function getAddress() {
    var address = '';
    $.ajax({
        url: '/device/generate_address',
        type: 'GET',
        async: false,
        success: function (data) {
            address = data["address"];
        }
    });
    return address;
}