function getUserDetails() {
    // Make an AJAX request to the backend to retrieve user details
    // You can use libraries like jQuery or fetch API for this

    // Example using fetch API
    fetch('/accounts/get_user_details') // Replace '/get_user_details' with the actual endpoint to retrieve user details
        .then(response => response.json())
        .then(data => {
            // Update the user picture
            const profilePicture = document.getElementById('profile-picture');
            profilePicture.src = data.userprofile.pict;

            // Update the user name
            const userName = document.getElementById('user-name');
            userName.textContent = `${data.userprofile.user.first_name} ${data.userprofile.user.last_name}`;

            // Update the user position
            const userPosition = document.getElementById('user-position');
            userPosition.textContent = data.userprofile.title;
        })
        .catch(error => {
            console.error('Error retrieving user details:', error);
        });
}

// Call the getUserDetails function when the page has finished loading
document.addEventListener('DOMContentLoaded', getUserDetails);
