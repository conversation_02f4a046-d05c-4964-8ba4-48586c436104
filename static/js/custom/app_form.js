const formContainer = document.querySelector('.card-body form');
let formData = {}; // Initialize an empty formData object

// Add a hidden input field to store formData as JSON
const formDataInput = document.createElement('input');
formDataInput.type = 'hidden';
formDataInput.name = 'app_config'; // This should match the name of the field in the form
formContainer.appendChild(formDataInput);

// Function to initialize formData with the initial values from the form inputs
function initializeFormData() {
    // Clone configData into formData
    formData = JSON.parse(JSON.stringify(configData));

    // Update the hidden input field with the JSON representation of formData
    formDataInput.value = JSON.stringify(formData);
}

for (const key in configTemplate) {
    const field = configTemplate[key];
    const fieldElement = document.createElement('div');
    fieldElement.className = 'form-group';

    if (field.type === 'bool') {
        const label = document.createElement('label');
        label.textContent = field.label;
        const input = document.createElement('input');
        input.type = 'checkbox';
        input.name = key;

        // if configData has value, assign it to field
        // otherwise assign default value
        if (configData.hasOwnProperty(key) && configData[key][0]) {
            input.checked = true;
            formData[key] = true; // Initialize formData with the initial values
        } else {
            formData[key] = false;
        }

        label.appendChild(input);
        fieldElement.appendChild(label);
    } else {
        const label = document.createElement('label');
        label.textContent = field.label;
        const input = document.createElement('input');
        input.type = 'number';
        input.name = key;
        // if configData has value, assign it to field
        // otherwise assign default value
        if (configData.hasOwnProperty(key)) {
            input.value = configData[key][0];
            formData[key] = configData[key]; // Initialize formData with the initial values
        } else {
            formData[key] = field.defaultValue; // Use field.defaultValue for default values
        }
        input.min = field.range[0];
        input.max = field.range[1];
        input.className = 'form-control';
        fieldElement.appendChild(label);
        fieldElement.appendChild(input);
    }

    const description = document.createElement('p');
    description.textContent = field.description;
    fieldElement.appendChild(description);

    formContainer.insertBefore(fieldElement, formContainer.querySelector('.row'));
}

initializeFormData();

// Function to update formData when form inputs change and set corrosponding sync status to false
function updateFormData(event) {
    const input = event.target;

    if (input.type === 'checkbox') {
        formData[input.name][0] = input.checked;
    } else {
        formData[input.name][0] = parseInt(input.value);
    }

    console.log(configData);

    // if changed, set sync status to false
    if (configData[input.name][0] != formData[input.name][0]) {
        formData[input.name][1] = false;
    } else {
        formData[input.name][1] = true;
    }

    // Update the hidden input field with the JSON representation of formData
    formDataInput.value = JSON.stringify(formData);

    console.log(formData);
}

// Add input event listeners to form inputs
const formInputs = document.querySelectorAll('.form-control, input[type="checkbox"]');
formInputs.forEach(input => {
    input.addEventListener('input', updateFormData);
});
