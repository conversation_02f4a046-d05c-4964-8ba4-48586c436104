document.addEventListener('DOMContentLoaded', function () {
  const wsScheme = window.location.protocol === "https:" ? "wss" : "ws";
  const wsPath = `${wsScheme}://${window.location.host}/ws/notifications/`;
  const userNotificationsSocket = new ReconnectingWebSocket(wsPath);

  let notifications = [];

  userNotificationsSocket.onmessage = function (event) {
    try {
      const message = JSON.parse(event.data);
      const eventType = message.type;

      if (eventType === 'init') {
        notifications = message.data;
      } else if (eventType === 'object_update') {
        notifications.unshift(message.data);
      }

      renderNotifications();
    } catch (e) {
      console.error("Error parsing message as JSON:", e, event.data);
    }
  };

  document.querySelector('#notification-container').addEventListener('click', async function (e) {
    const notificationLink = e.target.closest('.read-noti');
    if (notificationLink) {
      const notificationId = notificationLink.getAttribute('data-notification-id');
      const deviceId = notificationLink.getAttribute('data-device-id');

      await readNotification(notificationId, deviceId);
    }
  });

  function renderNotifications() {
    const todayFragment = document.createDocumentFragment();
    const yesterdayFragment = document.createDocumentFragment();
    const otherFragment = document.createDocumentFragment();

    const currentDate = new Date();

    for (const notification of notifications) {
      const date = new Date(notification.evnt.crat);

      let textColor, iconClass, iconColor;
      switch (notification.evnt.type) {
        case 'Info':
          textColor = 'text-info';
          iconClass = 'mdi-information-variant';
          iconColor = 'bg-info-lighten text-info';
          break;
        case 'Update':
          textColor = 'text-primary';
          iconClass = 'mdi-update';
          iconColor = 'bg-primary-lighten text-primary';
          break;
        case 'Warning':
          textColor = 'text-warning';
          iconClass = 'mdi-alert';
          iconColor = 'bg-warning-lighten text-warning';
          break;
        default:
          textColor = 'text-danger';
          iconClass = 'mdi-vibrate';
          iconColor = 'bg-danger-lighten text-danger';
      }

      const notificationHtml = `
          <div class="border rounded mb-1">
            <a href="#" class="dropdown-item p-0 notify-item card read-noti shadow-none my-0" data-notification-id="${notification.id}" data-device-id="${notification.evnt.devi.id}">
              <div class="card-body px-2 py-0 pb-1 m-0">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="notify-icon ${iconColor}">
                      <i class="mdi mdi-24px ${iconClass}"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 text-truncate ms-2">
                    <h5 class="noti-item-title fw-semibold font-14 mb-0 pb-0">${notification.evnt.devi.name}</h5>
                    <small class="noti-item-subtitle text-muted">${notification.evnt.desc}</small>
                  </div>
                </div>
              </div>
            </a>
          </div>
        `;

      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = notificationHtml;

      if (date.toLocaleDateString() === currentDate.toLocaleDateString()) {
        todayFragment.appendChild(tempDiv.firstElementChild);
      } else if (date.getDate() === currentDate.getDate() - 1) {
        yesterdayFragment.appendChild(tempDiv.firstElementChild);
      } else {
        otherFragment.appendChild(tempDiv.firstElementChild);
      }
    }

    const notificationContainer = document.querySelector('#notification-container');
    notificationContainer.innerHTML = '';

    if (todayFragment.childElementCount > 0) {
      notificationContainer.innerHTML += '<h5 class="text-muted font-13 fw-normal mt-2">Today</h5>';
      notificationContainer.appendChild(todayFragment);
    }

    if (yesterdayFragment.childElementCount > 0) {
      notificationContainer.innerHTML += '<h5 class="text-muted font-13 fw-normal mt-2">Yesterday</h5>';
      notificationContainer.appendChild(yesterdayFragment);
    }

    if (otherFragment.childElementCount > 0) {
      notificationContainer.innerHTML += '<h5 class="text-muted font-13 fw-normal mt-2">Older</h5>';
      notificationContainer.appendChild(otherFragment);
    }

    document.querySelector('#notification-badge').style.display = notificationContainer.childElementCount > 0 ? 'block' : 'none';
    document.querySelector('#no-notification-message').style.display = notificationContainer.childElementCount === 0 ? 'block' : 'none';
  }

  async function readNotification(notificationId, deviceId) {
    const csrfToken = getCookie("csrftoken");

    try {
      const redirectUrl = `/device/${deviceId}`;

      await fetch(`/notification_center/read/${notificationId}/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": csrfToken,
        },
      });

      window.location.href = redirectUrl;
    } catch (error) {
      console.error(error);
    }
  }

  function clearNotifications() {
    const csrfToken = getCookie("csrftoken");

    fetch('/notification_center/read/0/', {
      method: 'POST',
      headers: {
        "X-CSRFToken": csrfToken,
      },
    }).then(response => {
      if (response.ok) {
        notifications = [];
        renderNotifications();
      }
    })
      .catch(error => console.error(error));
  }

  function getCookie(name) {
    const cookieValue = document.cookie.match("(^|;)\\s*" + name + "\\s*=\\s*([^;]+)");
    return cookieValue ? cookieValue.pop() : "";
  }

  document.querySelector('#clear-notifications-btn').addEventListener('click', clearNotifications);
});
