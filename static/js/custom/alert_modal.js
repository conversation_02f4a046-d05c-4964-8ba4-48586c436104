function showModal(message) {
    // ...
    modal.style.display = "block";
}

// Handle the form submission with AJAX
var form = document.querySelector("form");
form.addEventListener("submit", function (event) {
    event.preventDefault();
    var formData = new FormData(form);
    var xhr = new XMLHttpRequest();
    xhr.open(form.method, form.action, true);
    xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");
    xhr.onload = function () {
        if (xhr.status >= 200 && xhr.status < 400) {
            var response = JSON.parse(xhr.responseText);
            showModal(response.message);
        } else {
            showModal("An error occurred while processing the request.");
        }
    };
    xhr.onerror = function () {
        showModal("An error occurred while processing the request.");
    };
    xhr.send(formData);
});