$(window).on('load', function () {
    const wsScheme = window.location.protocol === "https:" ? "wss" : "ws";
    const deviceWsPath = `${wsScheme}://${window.location.host}/ws/device/`;

    const deviceDataSocket = new ReconnectingWebSocket(deviceWsPath);

    let deviceListHtml = new Map();
    let deviceTimestamps = new Map(); // Store last update timestamps

    let noDevicesHtml = `
        <div class="d-flex flex-column justify-content-center align-items-center text-center">
            <i class="mdi mdi-magnify-close" style="font-size: 48px;"></i>
            <p>No devices found.</p>
        </div>
    `

    function updateTimestamps() {
        document.querySelectorAll('.device-last-update').forEach((element) => {
            const deviceId = element.getAttribute('data-id');
            const timestamp = deviceTimestamps.get(parseInt(deviceId));
            if (timestamp) {
                element.innerHTML = formatTimeElapsed(new Date(timestamp));
            }
        });

        // Update #last-update with the latest timestamp of the last device
        const deviceTimestampsArray = Array.from(deviceTimestamps.values());
        const lastDevice = deviceTimestampsArray[deviceTimestampsArray.length - 1];

        if (lastDevice) {
            const lastDeviceTimestamp = new Date(lastDevice);
            document.querySelector('#last-update').innerHTML = formatTimeElapsed(lastDeviceTimestamp);
        }
    }

    function getDeviceBadgeClass(status) {
        if (status === 'Online') {
            return 'badge badge-success-lighten';
        } else if (status === 'Warning') {
            return 'badge badge-warning-lighten';
        } else if (status === 'Offline') {
            return 'badge badge-secondary-lighten';
        } else {
            return 'badge badge-danger-lighten';
        }
    }

    function getBatteryBarColor(deviceBattery) {
        if (deviceBattery <= 25) {
            return "bg-danger";
        } else if (deviceBattery <= 50) {
            return "bg-warning";
        } else if (deviceBattery <= 100) {
            return "bg-success";
        }
    }

    deviceDataSocket.onmessage = function (event) {

        const devices = JSON.parse(event.data);

        // Update device timestamps and HTML
        if (Array.isArray(devices)) {
            devices.forEach((device) => {
                const deviceHtml = `
                <div class="d-flex align-items-start clickable mt-1 p-1 border" onclick="setFocus('${device.id}')">
                  <img class="me-3" src="/static/images/device/icons/${device.aset.toLowerCase().replace(" ", "-")}.png" width="38" alt="Generic placeholder image" />
                  <div class="w-100 overflow-hidden">
                    <span class="badge ${getDeviceBadgeClass(device.stat)} float-end">${device.stat}</span>
                    <h5 class="mt-0 mb-1">${device.name}${device.mntc ? '🚧' : ''}</h5>
                    <div class="row align-items-center">
                      <div class="col-sm-6">
                        <p class="mt-0 mb-0 device-last-update" data-id="${device.id}" style="font-size:12px;">${formatTimeElapsed(new Date(device.lupd))} ago</p>
                      </div>
                      <div class="col-sm-6">
                        <div class="progress progress-sm mt-1" style="background-color: #c5d3e2">
                          <i class="progress-bar progress-lg ${getBatteryBarColor(device.batt)}" style="width: ${device.batt}%"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              `;

                // Update device HTML and timestamp
                if (deviceListHtml.has(device.id)) {
                    deviceListHtml.delete(device.id);
                }

                deviceListHtml.set(device.id, deviceHtml);
                deviceTimestamps.set(device.id, device.lupd);

                updateMarker(device.id, device.name, device.loca.lati, device.loca.long, device.aset, device.mntc, device.stat);
            });
        }

        // Render updated device list
        document.querySelector('#asset-list-container').innerHTML = deviceListHtml.size === 0 ? noDevicesHtml : Array.from(deviceListHtml.values()).reverse().join('');
    };

    deviceDataSocket.onclose = function () {
        let connectionElement = document.querySelector('#ws-connection');
        connectionElement.innerHTML = 'DISCONNECTED';
        connectionElement.classList.remove('bg-success');
        connectionElement.classList.add('bg-secondary');
    };

    deviceDataSocket.onopen = function () {
        deviceDataSocket.send(JSON.stringify({ "fields": fieldIds }));

        let connectionElement = document.querySelector('#ws-connection');
        connectionElement.innerHTML = 'CONNECTED';
        connectionElement.classList.add('bg-success');
        connectionElement.classList.remove('bg-secondary');
    };

    // Update timestamps every second
    setInterval(updateTimestamps, 1000);
});
