let map;
let infowindow;
let fields = {};
let markers = {};
let infoWindows = {};

let mapInitialized = false;

let AdvancedMarkerElement;
let InfoWindowElement;

async function initMap() {
    // Request needed libraries.
    //@ts-ignore
    const { Map } = await google.maps.importLibrary("maps");
    const { AdvancedMarkerElement: AME } = await google.maps.importLibrary("marker");

    // Assign the imported libraries to global variables
    AdvancedMarkerElement = AME;

    map = new Map(document.getElementById('device_map'), {
        center: { lat: 0, lng: 0 },
        zoom: 8,
        mapId: "DETAIL"
    });

    mapInitialized = true;
}

function updatePolygon(fieldObject) {
    if (fieldObject.id in fields) {
        fields[fieldObject.id].setPaths(fieldObject.cord);
        fields[fieldObject.id].setOptions({
            strokeColor: fieldObject.colr,
            fillColor: fieldObject.colr,
        });
    } else {
        let field = new google.maps.Polygon({
            strokeOpacity: 0.8,
            strokeWeight: 2,
            fillOpacity: 0.35,
            editable: false,
            draggable: false
        });
        field.setMap(map);
        field.setPaths(fieldObject.cord);
        field.setOptions({
            strokeColor: fieldObject.colr,
            fillColor: fieldObject.colr,
        });

        fields[fieldObject.id] = field;
    }

    // recalculate map bounds
    recalcBounds();
}

function updateMarker(id, name, latitude, longitude, asset, maintenance, status) {
    function updateMarkerIcon() {
        // select marker icon based on device status
        var icon = asset.toLowerCase().replace(/\s+/g, '-');
        if (maintenance) {
            icon = icon + "-maintenance";
        } else {
            if (status == "Online") {
                icon = icon + "-success";
            } else if (status == "Offline") {
                icon = icon + "-offline";
            } else if (status == "Warning") {
                icon = icon + "-warning";
            } else {
                icon = icon + "-danger";
            }
        }

        // update icon
        const markerIcon = document.createElement("img");

        markerIcon.src = '/static/images/device/icons/' + icon + '.png';
        markerIcon.width = 48;

        return markerIcon;
    }

    function updateInfoWindow() {
        const contentString = `
            <div id="customInfoWindow" style="
                background-color: rgba(0, 0, 0, 0.8); 
                color: white; 
                padding: 10px; 
                border-radius: 5px;
                font-size: 1.1em; 
                position: relative;">
                <span style="font-weight: bold;">Name:     </span> <span>${name}</span><br>
                <span style="font-weight: bold;">Latitude: </span> <span>${latitude}</span><br>
                <span style="font-weight: bold;">Longitude:</span> <span>${longitude}</span>
            </div>
        `;
        return contentString;
    }

    // if marker id in markers, update marker
    if (id in markers) {
        markers[id].position = { lat: latitude, lng: longitude };
        markers[id].content = updateMarkerIcon();
        infoWindows[id].content = updateInfoWindow();
    }
    // else create new marker
    else {
        let marker = new AdvancedMarkerElement({
            map: map,
            position: { lat: latitude, lng: longitude },
            content: updateMarkerIcon(),
            title: name,
        });

        let infoWindow = new google.maps.InfoWindow({
            content: updateInfoWindow(),
        });

        marker.addListener('click', function () {
            infoWindow.open(map, marker);
        });

        markers[id] = marker;
        infoWindows[id] = infoWindow;
    }

    // recalculate map bounds
    recalcBounds();
}

function recalcBounds() {
    // Calculate the bounds of the polygon and get its center
    const bounds = new google.maps.LatLngBounds();
    const center = bounds.getCenter();

    for (const fieldId in fields) {
        fields[fieldId].getPath().forEach(latLng => bounds.extend(latLng));
    }

    for (const markerId in markers) {
        bounds.extend(markers[markerId].position);
    }

    // Set the center of the map to the polygon's center
    map.setCenter(center);

    // Adjust the viewport to contain the polygon
    map.fitBounds(bounds);
}

function setFocus(markerId) {
    // close all open info windows
    for (const id in markers) {
        infoWindows[id].close(map, markers[id]);
    }

    // Zoom in on the marker and open its info window
    map.setZoom(18);
    map.setCenter(markers[markerId].position);
    infoWindows[markerId].open(map, markers[markerId]);
}