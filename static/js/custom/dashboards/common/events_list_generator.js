$(document).ready(function () {
    const wsScheme = window.location.protocol === "https:" ? "wss" : "ws";
    const wsPath = `${wsScheme}://${window.location.host}/ws/events/`;
    const recentEventsSocket = new ReconnectingWebSocket(wsPath);

    let events = null;

    recentEventsSocket.onmessage = function (event) {
        event = JSON.parse(event.data);

        // if event is array
        if (Array.isArray(event)) {
            events = event;
            generateRecentActivities(events);
        } else {
            events = [...[event], ...events];
            // remove last event if more than 25 using pop
            if (events.length > 25) {
                events.pop();
            }
        }

        generateRecentActivities(events);
    };

    recentEventsSocket.onopen = function () {
        if (typeof deviceIds !== "undefined") {
            recentEventsSocket.send(JSON.stringify({ "devices": deviceIds }));
        } else {
            recentEventsSocket.send(JSON.stringify({ "fields": fieldIds }));
        }
    }
});

function generateRecentActivities(events) {
    const iconMapping = {
        'Info': { iconClass: 'mdi-information-variant', iconColor: 'bg-info-lighten text-info', textColor: 'text-info' },
        'Update': { iconClass: 'mdi-update', iconColor: 'bg-primary-lighten text-primary', textColor: 'text-primary' },
        'Warning': { iconClass: 'mdi-alert', iconColor: 'bg-warning-lighten text-warning', textColor: 'text-warning' },
        'Error': { iconClass: 'mdi-vibrate', iconColor: 'bg-danger-lighten text-danger', textColor: 'text-danger' }
    };

    const recentActivityHtml = events.length > 0
        ? events.map(event => {
            const { iconClass, iconColor, textColor } = iconMapping[event.type] || iconMapping['Error'];
            return `
                <div class="timeline-item">
                    <i class="mdi ${iconClass} ${iconColor} timeline-icon"></i>
                    <div class="timeline-item-info">
                        <p class="${textColor} fw-bold mb-1 d-block">${event.type}</p>
                        <small><a href="/device/${event.devi.id}"><span class="text-title fw-bold">${event.devi.name}</span></a> ${event.desc}</small>
                        <p class="mb-0 pb-2">
                            <small class="text-muted">${event.crat}</small>
                        </p>
                    </div>
                </div>`;
        }).join('')
        : `
            <div class="text-center">
                <i class="mdi mdi-magnify-close" style="font-size: 48px;"></i>
            </div>
            <p class="text-center">No recent activity found.</p>`;

    document.querySelector('#recent-activity-container').innerHTML = recentActivityHtml;
}