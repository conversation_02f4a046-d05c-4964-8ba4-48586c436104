$(window).on('load', function fetchFields() {
    const wsScheme = window.location.protocol === "https:" ? "wss" : "ws";
    const wsPath = `${wsScheme}://${window.location.host}/ws/field/`;
    const fieldSocket = new ReconnectingWebSocket(wsPath);

    fieldSocket.onmessage = function (event) {
        const fields = JSON.parse(event.data);

        if (Array.isArray(fields)) {
            fields.forEach((field) => {
                updatePolygon(field);
            });
        }
    };

    fieldSocket.onopen = function () {
        fieldSocket.send(JSON.stringify(fieldIds));
    }
});