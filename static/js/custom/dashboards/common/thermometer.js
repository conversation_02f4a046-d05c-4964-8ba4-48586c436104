var thermometerOptions = {
    series: [0],
    chart: {
        height: 250,
        type: 'radialBar',
    },
    plotOptions: {
        radialBar: {
            hollow: {
                margin: 0,
                size: '50%',
                background: '#fff',
                position: 'front',
                dropShadow: {
                    enabled: true,
                    top: 3,
                    left: 0,
                    blur: 4,
                    opacity: 0.24
                }
            },
            track: {
                background: '#fff',
                strokeWidth: '67%',
                margin: 0,
                dropShadow: {
                    enabled: true,
                    top: -3,
                    left: 0,
                    blur: 4,
                    opacity: 0.35
                }
            },

            dataLabels: {
                show: true,
                name: {
                    offsetY: -10,
                    show: true,
                    color: '#888',
                    fontSize: '17px'
                },
                value: {
                    formatter: function (val) {
                        return val;
                    },
                    color: '#111',
                    fontSize: '36px',
                    show: true,
                }
            }
        }
    },
    colors: [
        "#3688fc",
    ],
    fill: {
        type: 'solid',
    },
    stroke: {
        lineCap: 'round'
    },
    labels: ['Celsius'],
};

var thermometer = new ApexCharts(document.querySelector("#thermometer"), thermometerOptions);
thermometer.render();