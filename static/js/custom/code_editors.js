document.addEventListener("DOMContentLoaded", function () {
    var sharedConfig = {
        lineNumbers: true,
        mode: { name: "javascript", json: true },
        theme: "default",
        autoCloseBrackets: true,
        matchBrackets: true,
        indentUnit: 4,
        extraKeys: { "Tab": "autocomplete" },
    };

    var id_cfgt = document.getElementById("id_cfgt");
    if (id_cfgt) {
        if (isValidJson(id_cfgt.value)) {
            id_cfgt.value = JSON.stringify(JSON.parse(id_cfgt.value), null, 4);
        }
        var cfgtEditor = CodeMirror.fromTextArea(id_cfgt, sharedConfig);
    }

    var id_lutb = document.getElementById("id_lutb");
    if (id_lutb) {
        if (isValidJson(id_lutb.value)) {
            id_lutb.value = JSON.stringify(JSON.parse(id_lutb.value), null, 4);
        }
        var lutbEditor = CodeMirror.fromTextArea(id_lutb, sharedConfig);
    }

    var id_payload = document.getElementById("id_payload");
    if (id_payload) {
        if (isValidJson(id_payload.value)) {
            id_payload.value = JSON.stringify(JSON.parse(id_payload.value), null, 4);
        }
        var payloadEditor = CodeMirror.fromTextArea(id_payload, sharedConfig);
    }

    var id_chfg = document.getElementById("id_chfg");
    if (id_chfg) {
        if (isValidJson(id_chfg.value)) {
            id_chfg.value = JSON.stringify(JSON.parse(id_chfg.value), null, 4);
        }
        var chfgEditor = CodeMirror.fromTextArea(id_chfg, sharedConfig);
    }
});

function isValidJson(jsonString) {
    try {
        JSON.parse(jsonString);
        return true;
    } catch (error) {
        return false;
    }
}
