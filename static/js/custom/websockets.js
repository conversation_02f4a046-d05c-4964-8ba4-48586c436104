const eventClosureCauses = {
    1006: "Server reset",
    4001: "User unauthenticated",
    4002: "Requested object not found or user doesn't have access to it",
    4003: "Invalid request message"
};

class ReconnectingWebSocket {
    constructor(url, options = {}) {
        this.url = url;
        this.reconnectInterval = options.reconnectInterval || 5000; // Time in milliseconds before attempting to reconnect
        this.maxRetries = options.maxRetries || 3; // Maximum number of retries
        this.retryCount = 0;

        this.createWebSocket();
    }

    createWebSocket() {
        this.socket = new WebSocket(this.url);

        this.socket.onopen = (event) => {
            console.log(`WebSocket connection to ${this.url} established.`);
            this.retryCount = 0; // Reset the retry count upon successful connection
            if (this.onopen) this.onopen(event);
        };

        this.socket.onerror = (error) => {
            console.error(`WebSocket error on ${this.url}, reason: `, error);
            this.handleReconnect();
            if (this.onerror) this.onerror(error);
        };

        this.socket.onclose = (event) => {
            const reason = eventClosureCauses[event.code] ? eventClosureCauses[event.code] : `undefined error code [${event.code}]`;

            if (event.wasClean) {
                console.log(`WebSocket connection to ${this.url} closed cleanly, reason: ${reason}`);
            } else {
                console.error(`WebSocket connection to ${this.url} closed with error: ${reason}`);
                this.handleReconnect();
            }
            if (this.onclose) this.onclose(event);
        };

        this.socket.onmessage = (message) => {
            if (this.onmessage) this.onmessage(message);
        };
    }

    handleReconnect() {
        if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            console.log(`Attempting to reconnect to ${this.url}... (${this.retryCount}/${this.maxRetries})`);
            setTimeout(() => this.createWebSocket(), this.reconnectInterval);
        } else {
            console.error(`Max retries reached. Could not reconnect to ${this.url}.`);
        }
    }

    send(data) {
        if (this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(data);
        } else {
            console.error(`WebSocket to ${this.url} is not open. Ready state: ${this.socket.readyState}`);
        }
    }

    close(code, reason) {
        this.socket.close(code, reason);
    }
}