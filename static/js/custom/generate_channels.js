// Create the widgets
for (let i = 0; i <= 16; i++) {
    const widgetContainer = document.getElementById('channel-list');

    const divRow = document.createElement('div');
    divRow.className = 'row';

    const divWrapper = document.createElement('div');
    divWrapper.className = 'mb-2 mt-1';

    const divFlex = document.createElement('div');
    divFlex.className = 'd-flex align-items-center';

    const divInputGroup = document.createElement('div');
    divInputGroup.className = 'input-group flex-nowrap';
    divInputGroup.style.overflow = 'hidden';
    divInputGroup.style.flex = '1';

    const button = document.createElement('button');
    button.className = 'btn';
    button.type = 'button';
    button.id = `channel_${i}_toggle`;
    button.innerText = i;

    const spanValue = document.createElement('span');
    spanValue.innerHTML = `{{ form.channel${i}Value }}<span class="input-group-text" id="basic-addon1">MHz</span>`;

    divInputGroup.appendChild(button);
    divInputGroup.appendChild(spanValue);
    divFlex.appendChild(divInputGroup);
    divWrapper.appendChild(divFlex);
    divRow.appendChild(divWrapper);

    widgetContainer.appendChild(divRow);
}