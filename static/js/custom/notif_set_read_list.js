document.addEventListener("DOMContentLoaded", function () {
    // Find all the "Mark as Read" links
    const markAsReadLinks = document.querySelectorAll(".mark-as-read-link");

    markAsReadLinks.forEach(function (link) {
        link.addEventListener("click", function (event) {
            event.preventDefault(); // Prevent the link from triggering a regular page refresh

            // Get the notification ID from the link's ID attribute
            const notificationId = link.id.split("-")[1];

            // Get the CSRF token from the cookie
            const csrfToken = getCookie("csrftoken");

            // Send an AJAX request to mark the notification as read
            fetch("/notification_center/read/" + notificationId + "/", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRFToken": csrfToken,
                },
                body: JSON.stringify({ notificationId: notificationId }),
            })
                .then(function (response) {
                    if (response.ok) {
                        // Update the HTML dynamically
                        const tr = link.closest("tr");
                        tr.dataset.read = "read";
                        link.innerHTML = '<i class="mdi mdi-check-circle" style="opacity: 0.5;"></i>';
                    } else {
                        console.error("Failed to mark notification as read.");
                    }
                })
                .catch(function (error) {
                    console.error("An error occurred:", error);
                });
        });
    });
});

// Function to retrieve the CSRF token from the cookie
function getCookie(name) {
    const cookieValue = document.cookie.match("(^|;)\\s*" + name + "\\s*=\\s*([^;]+)");
    return cookieValue ? cookieValue.pop() : "";
}