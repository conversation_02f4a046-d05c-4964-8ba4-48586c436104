$(document).ready(function () {
    $('#devicesTable').DataTable({
        pageLength: 10,
        columnDefs: [{
            targets: 8,
            orderable: false
        }],
        language: {
            emptyTable: "No devices found, create one to get started."
        },
    });
    $('#deviceProfilesTable').DataTable({
        pageLength: 10,
        columnDefs: [{
            targets: 3,
            orderable: false
        }],
        language: {
            emptyTable: "No profiles found, create one to get started."
        },
    });
    $('#fieldsTable').DataTable({
        pageLength: 10,
        columnDefs: [{
            targets: 5,
            orderable: false
        }],
        language: {
            emptyTable: "No fields found, create one to get started."
        },
    });
    // Custom sorting function for "July 9, 2023, 12:45 p.m." format
    jQuery.extend(jQuery.fn.dataTableExt.oSort, {
        "date-custom-pre": function (a) {
            // Convert the date format directly into a standardized format like 'YYYY-MM-DD HH:MM' for efficient parsing.
            var dateParts = a.replace('a.m.', 'AM').replace('p.m.', 'PM'); // Standardize meridiem
            var parsedDate = new Date(dateParts);

            return parsedDate.getTime(); // Return the timestamp for efficient comparison
        },

        "date-custom-asc": function (a, b) {
            return a - b;
        },

        "date-custom-desc": function (a, b) {
            return b - a;
        }
    });

    // DataTable initialization
    $('#notificationsTable').DataTable({
        pageLength: 10,
        columnDefs: [{
            targets: 5,
            orderable: false
        }, {
            type: 'date-custom',
            targets: 0
        }],
        order: [[0, 'desc']],
        language: {
            emptyTable: "No notifications found."
        }
    });
    $('#uplinkTable').DataTable({
        pageLength: 10,
        columnDefs: [{
            type: 'date-custom',
            targets: 0
        }, {
            type: 'date-custom',
            targets: 1
        }],
        order: [[0, 'desc']],
        language: {
            emptyTable: "No packets received."
        },
    });
    $('#downlinkTable').DataTable({
        pageLength: 10,
        columnDefs: [{
            type: 'date-custom',
            targets: 0
        }],
        order: [[0, 'desc']],
        language: {
            emptyTable: "No packets transmitted."
        },
    });
    $('#droppedTable').DataTable({
        pageLength: 10,
        columnDefs: [{
            targets: [2, 3],
            orderable: false,
        }],
        order: [[0, 'desc']],
        language: {
            emptyTable: "No packets dropped."
        },
    });
    $('#userProfilesTable').DataTable({
        pageLength: 10,
        columnDefs: [{
            targets: 5,
            orderable: false
        }],
        language: {
            emptyTable: "No users found."
        },
    });
});