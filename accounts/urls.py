from django.urls import path
from .views import (
    AccountLoginView,
    RecoverPassword,
    UserListView,
    CreateFirstAdminUserView,
    UserProfileCreateView,
    UserProfileUpdateView,
    UserProfileViewer,
    get_user_details,
    logout_user,
    profile_delete,
)

app_name = "accounts"

urlpatterns = [
    path("create_admin", CreateFirstAdminUserView.as_view(), name="create_admin"),
    path("login/", AccountLoginView.as_view(), name="login"),
    path("list/", UserListView.as_view(), name="list"),
    path("edit/<int:profile_id>/", UserProfileUpdateView.as_view(), name="edit"),
    path("delete/<int:profile_id>/", profile_delete, name="delete"),
    path("create/", UserProfileCreateView.as_view(), name="create"),
    path("logout/", logout_user, name="logout"),
    path("view/", UserProfileViewer.as_view(), name="view"),
    path("recover.html/", RecoverPassword.as_view(), name="recover"),
    path("get_user_details", get_user_details, name="get_user_details"),
]
