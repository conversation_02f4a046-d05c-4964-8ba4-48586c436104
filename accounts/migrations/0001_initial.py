# Generated by Django 4.2.7 on 2024-05-02 15:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("device_manager", "0001_initial"),
        ("fields", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "phon",
                    models.Char<PERSON>ield(help_text="User phone number.", max_length=255),
                ),
                (
                    "pict",
                    models.ImageField(
                        blank=True, null=True, upload_to="profile_pictures"
                    ),
                ),
                ("titl", models.CharField(help_text="User title.", max_length=255)),
                (
                    "dprt",
                    models.CharField(
                        blank=True, help_text="Department", max_length=255, null=True
                    ),
                ),
                (
                    "devs",
                    models.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ield(
                        help_text="Devices the user has access to.",
                        to="device_manager.device",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        help_text="User profile owner.",
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
