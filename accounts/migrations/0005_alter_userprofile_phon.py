# Generated by Django 4.2.16 on 2025-04-10 12:41

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0004_userprofile_role'),
    ]

    operations = [
        migrations.AlterField(
            model_name='userprofile',
            name='phon',
            field=models.CharField(help_text='User phone number.', max_length=255, null=True, validators=[django.core.validators.RegexValidator(message="Must be entered in the format: '********'. 8 digits allowed.", regex='^\\d{8}$')]),
        ),
    ]
