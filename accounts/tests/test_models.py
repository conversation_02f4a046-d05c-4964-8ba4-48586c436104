from django.test import TestCase, tag
from django.contrib.auth.models import User
from accounts.models import UserProfile
from notification_center.models import NotificationSettings
from unittest.mock import patch, Mock
import uuid


class BaseUserProfileModelTest(TestCase):
    """Base class for user profile model tests with common setup"""
    def setUp(self):
        self.user = User.objects.create_user(
            username="testuser",
            password="testpassword",
            first_name="Test",
            last_name="User",
            email="<EMAIL>",
            is_superuser=False,
            is_staff=False
        )
        self.profile = UserProfile.objects.create(
            user=self.user,
            role="User",
            phon="**********",
            titl="Test Title",
            orgn="Test Organization"
        )
        self.notification_settings = NotificationSettings.objects.create(
            user=self.user
        )


@tag('unmock')
class UserProfileModelUnmockTest(BaseUserProfileModelTest):
    """Tests for user profile model without using mocks"""

    def test_profile_creation(self):
        """Test that a user profile can be created with the expected attributes"""
        self.assertEqual(self.profile.user.username, "testuser")
        self.assertEqual(self.profile.role, "User")
        self.assertEqual(self.profile.phon, "**********")
        self.assertEqual(self.profile.titl, "Test Title")
        self.assertEqual(self.profile.orgn, "Test Organization")

    def test_notification_settings_creation(self):
        """Test that notification settings are created correctly"""
        self.assertEqual(self.notification_settings.user, self.user)
        # Add assertions for default notification settings values

    def test_str_representation(self):
        """Test the string representation of a UserProfile object"""
        expected_str = f"{self.user.first_name} {self.user.last_name}'s Profile"
        self.assertEqual(str(self.profile), expected_str)


@tag('mock')
class UserProfileModelMockTest(TestCase):
    """Tests for user profile model using mocks"""

    def setUp(self):
        # Create a real user for testing
        self.user = User.objects.create_user(
            username="mockuser",
            password="mockpassword",
            first_name="Mock",
            last_name="User",
            email="<EMAIL>",
            is_superuser=True,
            is_staff=True
        )

    @patch('accounts.models.UserProfile.save')
    def test_profile_save_method_called(self, mock_save):
        """Test that the save method is called when creating a profile"""
        profile = UserProfile(
            user=self.user,
            role="Admin",
            phon="**********",
            titl="Mock Title",
            orgn="Mock Organization"
        )
        profile.save()
        mock_save.assert_called_once()

    @patch('uuid.uuid1')
    def test_profile_image_upload_with_mocked_uuid(self, mock_uuid1):
        """Test profile image upload with a mocked UUID"""
        mock_uuid = uuid.UUID('********-1234-5678-1234-************')
        mock_uuid1.return_value = mock_uuid

        # Create a real user for this test
        user = User.objects.create_user(
            username="imageuser",
            password="password",
            email="<EMAIL>"
        )

        profile = UserProfile(
            user=user,
            role="User",
            phon="**********",
            titl="Image Test",
            orgn="Image Org"
        )

        # Test with a mocked image upload process
        # In a real test, you would use SimpleUploadedFile
        self.assertEqual(str(mock_uuid1.return_value), '********-1234-5678-1234-************')