from django.http import JsonResponse
from accounts.models import UserProfile
from configuration.models import TYPE_DEFAULTS, get_network_configuration_template
from device_manager.models import Device
from django.urls import reverse_lazy, reverse
from django.core.paginator import Paginator
from django.views.generic import (
    ListView,
    CreateView,
    DetailView,
    UpdateView,
)
from django.shortcuts import get_object_or_404, redirect

from notification_center.models import Event
from .forms import DeviceCreateUpdateForm
from django.conf import settings
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.contrib.auth.decorators import user_passes_test


def get_last_update(device: Device):
    from datetime import datetime
    from django.utils.timesince import timesince

    if device.lupd:
        now = datetime.now()
        last_update = device.lupd
        return f"{timesince(last_update, now)}".split(",")[0]
    return "—"


@user_passes_test(lambda u: u.is_superuser)
def delete(request, device_id):
    device = get_object_or_404(Device, id=device_id)
    events = Event.objects.filter(devi=device_id)
    device.delete()
    events.delete()
    return redirect("device_manager:list")


@user_passes_test(lambda u: u.is_authenticated)
def toggle_hide(request, device_id):
    device = get_object_or_404(Device, id=device_id)

    device.hidn = not device.hidn
    device.save()

    Event(
        devi=device,
        type="Update",
        desc=f"{'hidden' if device.hidn else 'unhidden'} by {request.user.username}.",
    ).save()

    return redirect("device_manager:detail", device_id=device_id)


@user_passes_test(lambda u: u.is_authenticated)
def clear_status(request, device_id):
    device = get_object_or_404(Device, id=device_id)
    device.stat = "Online"
    device.actv = False

    device.attr["client"]["Shock event."] = False
    device.attr["client"]["Motion event."] = False
    device.attr["client"]["Motionless event."] = True

    device.save()

    Event(
        devi=device,
        type="Update",
        desc=f"status manually cleared by {request.user.username}.",
    ).save()

    return redirect("device_manager:detail", device_id=device_id)


@user_passes_test(lambda u: u.is_authenticated)
def clear_all_status(request):
    for device in Device.objects.all():
        device.stat = "Online"
        device.save()

        device.attr["client"]["Shock event."] = False
        device.attr["client"]["Motion Event"] = False

        Event(
            devi=device,
            type="Update",
            desc=f"status manually cleared by {request.user.username}.",
        ).save()

    return redirect("device_manager:list")


# decorator to check if user is logged in
@user_passes_test(lambda u: u.is_authenticated)
def toggle_maintenance(request, device_id):
    device = get_object_or_404(Device, id=device_id)
    profile = UserProfile.objects.get(user=request.user)

    if profile.role == "Admin" or profile.role == "Owner":
        device.mntc = not device.mntc
        device.save()

        Event(
            devi=device,
            type="Warning",
            desc=f"{'entered' if device.mntc else 'exited'} maintenance mode by {request.user.first_name} {request.user.last_name}.",
        ).save()
    else:
        Event(
            devi=device,
            type="Info",
            desc=f"request to {'disable' if device.mntc else 'enable'} maintenance mode by {request.user.first_name} {request.user.last_name}.",
        ).save()

    return redirect("device_manager:detail", device_id=device_id)


@user_passes_test(lambda u: u.is_superuser)
def generate_address(request):
    import random
    from django.db.models import Q

    address = None
    while True:
        address = hex(random.randint(0, 2**32 - 1))[2:].upper().zfill(8)
        if not Device.objects.filter(Q(addr=address)).exists():
            break
    return JsonResponse({"address": address})


@method_decorator(login_required, name="dispatch")
class DeviceListView(LoginRequiredMixin, ListView):
    model = Device
    template_name = "device_manager/list.html"
    context_object_name = "list"

    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user
        if user.is_superuser:
            return queryset.order_by("name")
        return queryset.filter(userprofile__user=user).order_by("name")

    def test_func(self):
        return self.request.user.is_superuser

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context


@method_decorator(user_passes_test(lambda u: u.is_superuser), name="dispatch")
class DeviceCreateView(CreateView):
    model = Device
    form_class = DeviceCreateUpdateForm
    template_name = "device_manager/create.html"
    success_url = reverse_lazy("device_manager:list")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["GOOGLE_MAPS_API_KEY"] = settings.GOOGLE_MAPS_API_KEY
        return context


@method_decorator(login_required, name="dispatch")
class DeviceDetailView(LoginRequiredMixin, DetailView):
    model = Device
    pk_url_kwarg = "device_id"
    context_object_name = "device"

    def dispatch(self, request, *args, **kwargs):
        self.object = self.get_object()
        user = request.user
        if (
            user.is_superuser
            or user.userprofile.devs.filter(id=self.object.id).exists()
        ):
            return super().dispatch(request, *args, **kwargs)
        else:
            return redirect("accounts:login")

    def get_template_names(self):
        device = self.get_object()
        self.template_name = (
            "device_manager/dashboards/"
            + device.type.replace(" ", "-").lower()
            + ".html"
        )
        return [self.template_name]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["GOOGLE_MAPS_API_KEY"] = settings.GOOGLE_MAPS_API_KEY
        return context


class DeviceUpdateView(UpdateView):
    model = Device
    form_class = DeviceCreateUpdateForm
    template_name = "device_manager/create.html"
    pk_url_kwarg = "device_id"
    context_object_name = "device"
    success_url = reverse_lazy("device_manager:list")
    next = None

    def dispatch(self, request, *args, **kwargs):
        try:
            self.next = request.META["QUERY_STRING"].split("=")[1]
        except:
            pass

        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["GOOGLE_MAPS_API_KEY"] = settings.GOOGLE_MAPS_API_KEY
        return context

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        form.fields["type"].disabled = True

        return form

    # set field oof to false when device is updated
    def form_valid(self, form):
        device = form.save(commit=False)
        device.loca["oofi"] = False
        device.save()

        return super().form_valid(form)
