import json
from accounts.models import UserProfile
from auth.scripts import check_user_auth, fetch_devices
from channels.generic.websocket import WebsocketConsumer
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from device_manager.models import Device
from django.forms.models import model_to_dict
import logging

logger = logging.getLogger("app")


class DeviceConsumer(WebsocketConsumer):
    def connect(self):
        self.user = self.scope["user"]

        self.accept()

        check_user_auth(self)

    def disconnect(self, close_code):
        # Leave field group if it exists
        if hasattr(self, "devices"):
            devices = getattr(self, "devices", [])
            for device in devices:
                async_to_sync(get_channel_layer().group_discard)(
                    f"device_{device.id}", self.channel_name
                )

    def receive(self, text_data):
        fetch_devices(self, text_data)

        self.handle_subscription()
        self.send_initial_data()

    def handle_subscription(self):
        for device in self.devices:
            async_to_sync(get_channel_layer().group_add)(
                f"device_{device.id}", self.channel_name
            )

    def send_initial_data(self):
        devices_list = [device.to_dict() for device in self.devices]

        self.send(json.dumps(devices_list))

    def object_update(self, event):
        self.send(text_data=json.dumps([event["data"]]))
