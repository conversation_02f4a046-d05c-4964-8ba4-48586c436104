# Generated by Django 4.2.7 on 2024-05-05 03:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('device_manager', '0005_alter_device_actv'),
    ]

    operations = [
        migrations.AddField(
            model_name='device',
            name='batt',
            field=models.IntegerField(default=0, help_text='Battery level of the device'),
        ),
        migrations.AddField(
            model_name='device',
            name='temp',
            field=models.IntegerField(default=0, help_text='Temperature of the device'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='device',
            name='attr',
            field=models.J<PERSON><PERSON>ield(default=dict, help_text="JSON object representing the device's attributes_templates"),
        ),
    ]
