# Generated by Django 4.2.7 on 2024-05-02 15:37

import device_manager.models
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('fields', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Device',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of the device', max_length=255)),
                ('desc', models.TextField(blank=True, help_text='Description of the device')),
                ('euid', models.CharField(help_text='EUI-64 address of the device', max_length=16, validators=[django.core.validators.RegexValidator(message='Dev key should only consist of (A-F) and (0-9) and be 16 characters long.', regex='^[A-Fa-f0-9]{16}$')])),
                ('stat', models.Char<PERSON>ield(default='Offline', help_text='Status of the device', max_length=255)),
                ('attr', models.JSO<PERSON>ield(help_text="JSON object representing the device's attributes")),
                ('cfgs', models.JSONField(default=dict, help_text="JSON object representing the device's configuration")),
                ('type', models.CharField(help_text='Type of the device', max_length=255)),
                ('aset', models.CharField(default='Other', help_text='Type of asset the device is installed on', max_length=20)),
                ('loca', models.JSONField(default=device_manager.models.get_location_template, help_text="JSON object representing the device's location")),
                ('offp', models.IntegerField(default=0, help_text='Amount of time to wait for since last update for the device to be considered offline')),
                ('crat', models.DateTimeField(auto_now_add=True, help_text='Time the device was created')),
                ('lupd', models.DateTimeField(blank=True, help_text='Last time the device was updated', null=True)),
                ('fild', models.ForeignKey(default=None, help_text='The field which this device is assigned to', on_delete=django.db.models.deletion.CASCADE, related_name='devices', to='fields.field')),
            ],
            options={
                'verbose_name': 'Device',
                'verbose_name_plural': 'Devices',
            },
        ),
    ]
