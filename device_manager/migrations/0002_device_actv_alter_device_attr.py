# Generated by Django 4.2.7 on 2024-05-02 16:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('device_manager', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='device',
            name='actv',
            field=models.BooleanField(default=True, help_text='Whether the device is active or not'),
        ),
        migrations.AlterField(
            model_name='device',
            name='attr',
            field=models.JSONField(default={}, help_text="JSON object representing the device's attributes"),
        ),
    ]
