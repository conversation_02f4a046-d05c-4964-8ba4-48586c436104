from datetime import <PERSON>elta
import json
from django.forms import (
    ModelForm,
    TextInput,
    Select,
    NumberInput,
    CharField,
    ChoiceField,
    FloatField,
    Textarea,
    IntegerField,
)
from .models import Device
from .models import Device
from fields.models import Field
from django.core.exceptions import ValidationError


class DeviceCreateUpdateForm(ModelForm):
    FORM_CONTROL = "form-control"
    FORM_SELECT = "form-select col-sm-2"
    TYPE_CHOICES = [
        ("Whiskers Node V1", "Whiskers Node V1"),
        ("Whiskers Gateway V1", "Whiskers Gateway V1"),
    ]
    ASSET_CHOICES = [
        ("Battery", "Battery"),
        ("Box", "Box"),
        ("Cable", "Cable"),
        ("Drum", "Drum"),
        ("Person", "Person"),
        ("Solar Panel", "Solar Panel"),
        ("Station", "Station"),
        ("Vehicle", "Vehicle"),
        ("Other", "Other"),
    ]
    STATUS_CHOICES = [
        ("Online", "Online"),
        ("Offline", "Offline"),
        ("Warning", "Warning"),
        ("Danger", "Danger"),
        ("Maintenance", "Maintenance"),
    ]
    PLACEMENT_CHOICES = [
        ("Indoor", "Indoor"),
        ("Outdoor", "Outdoor"),
        ("Other", "Other"),
    ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["fild"].choices = Field.objects.all().values_list("id", "name")

    name = CharField(
        widget=TextInput(attrs={"class": FORM_CONTROL, "maxlength": 25}),
        label="Name",
    )
    desc = CharField(
        widget=Textarea(
            attrs={
                "class": FORM_CONTROL,
                "rows": 5,
                "maxlength": 2048,
            }
        ),
        label="Description",
        required=False,
    )
    euid = CharField(
        widget=TextInput(
            attrs={
                "class": FORM_CONTROL,
                "maxlength": 51,
                "data-toggle": "input-mask",
                "data-mask-format": "AA  •  AA  •  AA  •  AA  •  AA  •  AA  •  AA  •  AA",
                "placeholder": "--  •  --  •  --  •  --  •  --  •  --  •  --  •  --",
            }
        ),
        label="EUI-64",
    )
    type = ChoiceField(
        widget=Select(attrs={"class": FORM_SELECT}), choices=TYPE_CHOICES, label="Type"
    )
    aset = ChoiceField(
        widget=Select(attrs={"class": FORM_SELECT}),
        choices=ASSET_CHOICES,
        label="Asset",
    )
    lati = FloatField(
        widget=NumberInput(attrs={"class": FORM_CONTROL}), label="Latitude", initial=0.0
    )
    long = FloatField(
        widget=NumberInput(attrs={"class": FORM_CONTROL}),
        label="Longitude",
        initial=0.0,
    )
    alti = FloatField(
        widget=NumberInput(attrs={"class": FORM_CONTROL}), label="Altitude", initial=0.0
    )
    plac = ChoiceField(
        widget=Select(attrs={"class": FORM_SELECT}),
        choices=PLACEMENT_CHOICES,
        label="Placement",
    )
    loca = CharField(widget=TextInput(attrs={"hidden": True}), required=False)
    fild = ChoiceField(
        widget=Select(attrs={"class": FORM_SELECT}),
        label="Assigned Field",
    )
    offp = IntegerField(
        widget=NumberInput(attrs={"class": FORM_CONTROL}),
        label="Offline Period",
    )

    class Meta:
        model = Device
        fields = [
            "name",
            "desc",
            "type",
            "aset",
            "loca",
            "euid",
            "plac",
            "fild",
            "offp",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["fild"].choices = Field.objects.all().values_list("id", "name")

        if self.instance and self.instance.pk:
            self.fields["lati"].initial = self.instance.loca.get("lati", 0.0)
            self.fields["long"].initial = self.instance.loca.get("long", 0.0)
            self.fields["alti"].initial = self.instance.loca.get("alti", 0.0)
            self.fields["plac"].initial = self.instance.loca.get("plac", "Other")

    def clean(self):
        cleaned_data = super().clean()
        cleaned_data["loca"] = {
            "lati": cleaned_data.pop("lati"),
            "long": cleaned_data.pop("long"),
            "alti": cleaned_data.pop("alti"),
            "plac": cleaned_data.pop("plac"),
            "oofi": False,
        }
        cleaned_data["euid"] = cleaned_data["euid"].upper().replace("  •  ", "")
        return cleaned_data

    def clean_fild(self):
        fild_id = self.cleaned_data.get("fild")
        try:
            fild = Field.objects.get(pk=fild_id)  # replace Field with the correct model
        except Field.DoesNotExist:  # replace Field with the correct model
            raise ValidationError("Field with this ID does not exist.")
        return fild
