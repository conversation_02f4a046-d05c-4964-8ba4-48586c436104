from .models import Device
from .models import attributes_templates


def make_attr_connections(device: Device):
    for key, pointer in attributes_templates[device.type]["conc"].items():
        if type(pointer) is not dict:
            if pointer in device.attr["client"].keys():
                device.__setattr__(key, device.attr["client"][pointer])
        else:
            for sub_key, sub_pointer in pointer.items():
                if sub_pointer in device.attr["client"].keys():
                    device.__getattribute__(key)[sub_key] = device.attr["client"][
                        sub_pointer
                    ]
