from django import forms
from .models import DecodedPacket, Command, Data


class DecodedPacketCreateForm(forms.ModelForm):
    class Meta:
        model = DecodedPacket
        fields = [
            "devi",
            "mtyp",
            "drct",
        ]
        labels = {
            "devi": "Target Device",
            "mtyp": "Message Type",
            "drct": "Message Direction",
        }
        widgets = {
            "devi": forms.Select(attrs={"class": "form-select"}),
            "mtyp": forms.Select(attrs={"class": "form-select"}),
            "wwvr": forms.NumberInput(attrs={"class": "form-control"}),
            "drct": forms.Select(attrs={"class": "form-select"}),
        }


class CommandCreateForm(forms.ModelForm):
    class Meta:
        model = Command
        fields = ["ctyp", "kwrd", "valu"]
        labels = {
            "ctyp": "Command Type",
            "kwrd": "Command Keyword",
            "valu": "Command Value",
        }
        widgets = {
            "ctyp": forms.Select(attrs={"class": "form-select"}),
            "kwrd": forms.TextInput(attrs={"class": "form-control"}),
            "valu": forms.NumberInput(attrs={"class": "form-control"}),
        }
