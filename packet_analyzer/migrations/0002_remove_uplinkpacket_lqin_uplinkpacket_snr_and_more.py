# Generated by Django 4.2.7 on 2024-05-05 03:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('packet_analyzer', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='uplinkpacket',
            name='lqin',
        ),
        migrations.AddField(
            model_name='uplinkpacket',
            name='snr',
            field=models.FloatField(default=0, help_text='Signal-to-Noise Ratio (SNR) of the received packet'),
        ),
        migrations.AlterField(
            model_name='uplinkpacket',
            name='rssi',
            field=models.IntegerField(default=0, help_text='Received Signal Strength Indicator (RSSI)'),
        ),
    ]
