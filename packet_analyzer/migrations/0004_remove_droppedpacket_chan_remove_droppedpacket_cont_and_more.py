# Generated by Django 5.0.6 on 2024-05-28 19:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('packet_analyzer', '0003_downlinkpacket_chan_downlinkpacket_freq_and_more'),
    ]

    operations = [
        migrations.Remove<PERSON>ield(
            model_name='droppedpacket',
            name='chan',
        ),
        migrations.RemoveField(
            model_name='droppedpacket',
            name='cont',
        ),
        migrations.RemoveField(
            model_name='droppedpacket',
            name='devi',
        ),
        migrations.RemoveField(
            model_name='droppedpacket',
            name='freq',
        ),
        migrations.RemoveField(
            model_name='droppedpacket',
            name='gate',
        ),
        migrations.RemoveField(
            model_name='droppedpacket',
            name='lqin',
        ),
        migrations.RemoveField(
            model_name='droppedpacket',
            name='rssi',
        ),
        migrations.RemoveField(
            model_name='droppedpacket',
            name='txat',
        ),
        migrations.AlterField(
            model_name='droppedpacket',
            name='data',
            field=models.JSONField(help_text='JSON object representing the dropped packet'),
        ),
    ]
