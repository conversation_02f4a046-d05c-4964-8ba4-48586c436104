# Generated by Django 4.2.16 on 2025-04-10 12:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('device_manager', '0010_remove_device_cfgs'),
        ('packet_analyzer', '0005_alter_droppedpacket_expt'),
    ]

    operations = [
        migrations.AddField(
            model_name='droppedpacket',
            name='devi',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='devi_dropped', to='device_manager.device'),
        ),
        migrations.AddField(
            model_name='droppedpacket',
            name='gate',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='gate_dropped', to='device_manager.device'),
        ),
    ]