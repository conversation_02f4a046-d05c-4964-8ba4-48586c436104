# Generated by Django 4.2.7 on 2024-05-02 15:37

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('device_manager', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Command',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ctyp', models.CharField(choices=[('NC', 'Network Command'), ('DC', 'Device Command')], help_text='The type of command: Network Command or Device Command', max_length=2)),
                ('kwrd', models.CharField(help_text='The keyword associated with the command', max_length=255)),
                ('valu', models.IntegerField(help_text='The value associated with the command')),
                ('crat', models.DateTimeField(auto_now_add=True, help_text='The date and time the command was created')),
                ('atmt', models.IntegerField(default=0, help_text='The number of transmission attempts for the command')),
                ('akat', models.DateTimeField(blank=True, help_text='The date and time the command was acknowledged', null=True)),
                ('devi', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='commands', to='device_manager.device')),
            ],
        ),
        migrations.CreateModel(
            name='Data',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('kwrd', models.CharField(help_text='The keyword representing the data name', max_length=255)),
                ('valu', models.IntegerField(help_text='The actual data value stored as an integer')),
                ('vtyp', models.CharField(choices=[('INT', 'Integer'), ('FLT', 'Float'), ('BLN', 'Boolean')], help_text='The type of data value: Integer, Float or Boolean', max_length=3)),
                ('tmst', models.DateTimeField(auto_now_add=True, help_text='The timestamp for the data value')),
                ('devi', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='data', to='device_manager.device')),
            ],
        ),
        migrations.CreateModel(
            name='UplinkPacket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data', models.CharField(help_text='Hexadecimal string representing the packet', max_length=255)),
                ('deco', models.JSONField(help_text='JSON object representing the decoded packet')),
                ('cont', models.PositiveIntegerField(default=0, help_text='Packet count')),
                ('rssi', models.FloatField(default=0, help_text='Received Signal Strength Indicator (RSSI)')),
                ('lqin', models.PositiveIntegerField(default=0, help_text='Link Quality Indicator (LQI)')),
                ('chan', models.PositiveIntegerField(default=0, help_text='Channel number')),
                ('freq', models.PositiveIntegerField(default=0, help_text='Center frequency of the transmitted packet')),
                ('txat', models.DateTimeField(help_text='Datetime when the packet was transmitted by the device')),
                ('rxat', models.DateTimeField(default=django.utils.timezone.now, help_text='Datetime when the packet has arrived at the network server')),
                ('devi', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='devi_uplink', to='device_manager.device')),
                ('gate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gate_uplink', to='device_manager.device')),
            ],
        ),
        migrations.CreateModel(
            name='DroppedPacket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data', models.CharField(help_text='Hexadecimal string representing the packet', max_length=255)),
                ('cont', models.PositiveIntegerField(default=0, help_text='Packet count')),
                ('rssi', models.FloatField(default=0, help_text='Received Signal Strength Indicator (RSSI)')),
                ('lqin', models.PositiveIntegerField(default=0, help_text='Link Quality Indicator (LQI)')),
                ('chan', models.PositiveIntegerField(default=0, help_text='Channel number')),
                ('freq', models.FloatField(default=0, help_text='Center frequency of the transmitted packet')),
                ('expt', models.CharField(help_text='Reason for dropping the packet', max_length=255)),
                ('txat', models.DateTimeField(help_text='Datetime when the packet was transmitted by the device')),
                ('rxat', models.DateTimeField(default=django.utils.timezone.now, help_text='Datetime when the packet was arrived at the network server')),
                ('devi', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='devi_dropped', to='device_manager.device')),
                ('gate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gate_dropped', to='device_manager.device')),
            ],
        ),
        migrations.CreateModel(
            name='DownlinkPacket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data', models.CharField(help_text='Hexadecimal string representing the packet', max_length=255)),
                ('deco', models.JSONField(help_text='JSON object representing the decoded packet')),
                ('cont', models.PositiveIntegerField(default=0, help_text='Packet count')),
                ('crat', models.DateTimeField(default=django.utils.timezone.now, help_text='Datetime when the packet was created by the network server')),
                ('txat', models.DateTimeField(help_text='Datetime when the packet left the network server')),
                ('devi', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='devi_downlink', to='device_manager.device')),
                ('gate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gate_downlink', to='device_manager.device')),
            ],
        ),
        migrations.CreateModel(
            name='DecodedPacket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mtyp', models.CharField(choices=[('JR', 'Join Request'), ('JA', 'Join Accept'), ('UDU', 'Unconfirmed Data Up'), ('UDD', 'Unconfirmed Data Down'), ('CDU', 'Confirmed Data Up'), ('CDD', 'Confirmed Data Down'), ('RJR', 'Rejoin Request'), ('RT', 'Routing')], help_text='The type of message in the packet: Join Request, Join Accept, Unconfirmed Data Up, Unconfirmed Data Down, Confirmed Data Up, Confirmed Data Down, Rejoin Request, or Routing', max_length=3)),
                ('drct', models.CharField(default='Downlink', help_text='The message direction: Uplink or Downlink', max_length=8)),
                ('adpt', models.BooleanField(help_text='Whether adaptive data rate is enabled in the packet')),
                ('ackn', models.BooleanField(help_text='Whether acknowledgement is enabled in the packet')),
                ('cmds', models.ManyToManyField(help_text='The commands included in the packet', related_name='decoded_packets', to='packet_analyzer.command')),
                ('data', models.ManyToManyField(help_text='The data included in the packet', related_name='decoded_packets', to='packet_analyzer.data')),
                ('devi', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='decoded_packets', to='device_manager.device')),
            ],
        ),
    ]
