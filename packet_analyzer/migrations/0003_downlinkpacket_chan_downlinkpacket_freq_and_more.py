# Generated by Django 4.2.7 on 2024-05-05 17:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('packet_analyzer', '0002_remove_uplinkpacket_lqin_uplinkpacket_snr_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='downlinkpacket',
            name='chan',
            field=models.PositiveIntegerField(default=0, help_text='Channel number'),
        ),
        migrations.AddField(
            model_name='downlinkpacket',
            name='freq',
            field=models.PositiveIntegerField(default=0, help_text='Center frequency of the transmitted packet'),
        ),
        migrations.AddField(
            model_name='downlinkpacket',
            name='txpr',
            field=models.FloatField(default=0, help_text='Transmit power of the packet'),
        ),
    ]
