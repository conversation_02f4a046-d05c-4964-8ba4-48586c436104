from django.utils import timezone
from django.db import models
from device_manager.models import Device


# Create your models here.
class UplinkPacket(models.Model):
    devi = models.ForeignKey(
        Device, on_delete=models.CASCADE, related_name="devi_uplink"
    )
    gate = models.ForeignKey(
        Device, on_delete=models.CASCADE, related_name="gate_uplink"
    )
    data = models.CharField(
        max_length=255, help_text="Hexadecimal string representing the packet"
    )
    deco = models.JSONField(help_text="JSON object representing the decoded packet")
    cont = models.PositiveIntegerField(default=0, help_text="Packet count")
    rssi = models.IntegerField(
        default=0, help_text="Received Signal Strength Indicator (RSSI)"
    )
    snr = models.FloatField(
        default=0, help_text="Signal-to-Noise Ratio (SNR) of the received packet"
    )
    chan = models.PositiveIntegerField(default=0, help_text="Channel number")
    freq = models.PositiveIntegerField(
        default=0, help_text="Center frequency of the transmitted packet"
    )
    txat = models.DateTimeField(
        help_text="Datetime when the packet was transmitted by the device"
    )
    rxat = models.DateTimeField(
        default=timezone.now,
        help_text="Datetime when the packet has arrived at the network server",
    )

    def __str__(self):
        return f"Uplink Packet No. {self.cont} from '{self.devi.name}' of address '{self.devi.addr}' through '{self.gate.name}' on channel {self.chan} with RSSI {self.rssi}, contents: {self.deco}"


class DownlinkPacket(models.Model):
    devi = models.ForeignKey(
        Device, on_delete=models.CASCADE, related_name="devi_downlink"
    )
    gate = models.ForeignKey(
        Device, on_delete=models.CASCADE, related_name="gate_downlink"
    )
    data = models.CharField(
        max_length=255, help_text="Hexadecimal string representing the packet"
    )
    deco = models.JSONField(help_text="JSON object representing the decoded packet")
    cont = models.PositiveIntegerField(default=0, help_text="Packet count")
    chan = models.PositiveIntegerField(default=0, help_text="Channel number")
    freq = models.PositiveIntegerField(
        default=0, help_text="Center frequency of the transmitted packet"
    )
    txpr = models.FloatField(default=0, help_text="Transmit power of the packet")
    crat = models.DateTimeField(
        default=timezone.now,
        help_text="Datetime when the packet was created by the network server",
    )
    txat = models.DateTimeField(
        help_text="Datetime when the packet left the network server"
    )

    def __str__(self):
        return f"Downlink Packet No. {self.cont} to '{self.devi.name}' of address '{self.devi.addr}' through '{self.gate.name}', contents: {self.deco}"


class DroppedPacket(models.Model):
    devi = models.ForeignKey(
        Device, on_delete=models.CASCADE, related_name="devi_dropped", null=True, blank=True
    )
    gate = models.ForeignKey(
        Device, on_delete=models.CASCADE, related_name="gate_dropped", null=True, blank=True
    )
    data = models.JSONField(help_text="JSON object representing the dropped packet")
    expt = models.CharField(help_text="Reason for dropping the packet")
    rxat = models.DateTimeField(
        default=timezone.now,
        help_text="Datetime when the packet was arrived at the network server",
    )

    def __str__(self):
        return f"Dropped Packet No. {self.id}, reason: {self.expt}"


class Command(models.Model):
    CTYP_CHOICES = (
        ("NC", "Network Command"),
        ("DC", "Device Command"),
    )

    devi = models.ForeignKey(
        Device,
        default=None,
        blank=True,
        null=True,
        on_delete=models.CASCADE,
        related_name="commands",
    )
    ctyp = models.CharField(
        max_length=2,
        choices=CTYP_CHOICES,
        help_text="The type of command: Network Command or Device Command",
    )
    kwrd = models.CharField(
        max_length=255, help_text="The keyword associated with the command"
    )
    valu = models.IntegerField(help_text="The value associated with the command")
    crat = models.DateTimeField(
        auto_now_add=True, help_text="The date and time the command was created"
    )
    atmt = models.IntegerField(
        default=0, help_text="The number of transmission attempts for the command"
    )
    akat = models.DateTimeField(
        null=True,
        blank=True,
        help_text="The date and time the command was acknowledged",
    )

    def __str__(self):
        return f"{self.ctyp} {self.kwrd} {self.valu}"


class Data(models.Model):
    VTYP_CHOICES = (
        ("INT", "Integer"),
        ("FLT", "Float"),
        ("BLN", "Boolean"),
    )

    devi = models.ForeignKey(
        Device,
        default=None,
        blank=True,
        null=True,
        on_delete=models.CASCADE,
        related_name="data",
    )
    kwrd = models.CharField(
        max_length=255, help_text="The keyword representing the data name"
    )
    valu = models.IntegerField(help_text="The actual data value stored as an integer")
    vtyp = models.CharField(
        max_length=3,
        choices=VTYP_CHOICES,
        help_text="The type of data value: Integer, Float or Boolean",
    )
    tmst = models.DateTimeField(
        auto_now_add=True, help_text="The timestamp for the data value"
    )

    def __str__(self):
        return f"{self.kwrd} {self.valu}"


class DecodedPacket(models.Model):
    MTYP_CHOICES = (
        ("JR", "Join Request"),
        ("JA", "Join Accept"),
        ("UDU", "Unconfirmed Data Up"),
        ("UDD", "Unconfirmed Data Down"),
        ("CDU", "Confirmed Data Up"),
        ("CDD", "Confirmed Data Down"),
        ("RJR", "Rejoin Request"),
        ("RT", "Routing"),
    )

    devi = models.ForeignKey(
        Device,
        default=None,
        blank=True,
        null=True,
        on_delete=models.CASCADE,
        related_name="decoded_packets",
    )
    mtyp = models.CharField(
        max_length=3,
        choices=MTYP_CHOICES,
        help_text="The type of message in the packet: Join Request, Join Accept, Unconfirmed Data Up, Unconfirmed Data Down, Confirmed Data Up, Confirmed Data Down, Rejoin Request, or Routing",
    )
    drct = models.CharField(
        default="Downlink",
        max_length=8,
        help_text="The message direction: Uplink or Downlink",
    )
    adpt = models.BooleanField(
        help_text="Whether adaptive data rate is enabled in the packet"
    )
    ackn = models.BooleanField(
        help_text="Whether acknowledgement is enabled in the packet"
    )
    cmds = models.ManyToManyField(
        "Command",
        related_name="decoded_packets",
        help_text="The commands included in the packet",
    )
    data = models.ManyToManyField(
        "Data",
        related_name="decoded_packets",
        help_text="The data included in the packet",
    )

    def __str__(self):
        return f"{self.mtyp} to {self.devi}"