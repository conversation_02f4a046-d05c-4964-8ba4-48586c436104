from datetime import datetime
import json
import logging
from django.db.models import Q

from accounts.models import UserProfile
from device_manager.models import Device

logger = logging.getLogger("app")


def check_user_auth(instance):
    if not instance.user.is_authenticated:
        instance.close(code=4001)


def fetch_devices(instance, text_data):
    try:
        # Load the JSON data
        data = json.loads(text_data)

        # Check for expected keys and their corresponding values
        if "devices" in data:
            # verify devices contains list of ids
            if not isinstance(data["devices"], list):
                raise ValueError("Expected 'devices' to be a list")

            instance.devices = Device.objects.filter(
                id__in=data["devices"]
            ).order_by("lupd")
        elif "fields" in data:
            # verify fields contains list of ids
            if not isinstance(data["fields"], list):
                raise ValueError("Expected 'fields' to be a list")

            # get list of devices that are assigned to the field ids and owned by the user
            instance.devices = Device.objects.filter(
                fild__in=data["fields"], hidn=False
            ).order_by("lupd")
        else:
            raise ValueError("Expected 'devices' or 'fields' key in JSON data")

        # filter out devices by userprofile if not super admin
        if not instance.user.is_superuser:
            instance.devices = instance.devices.filter(userprofile__user=instance.user)

    except (json.JSONDecodeError, ValueError) as e:
        logger.error("Invalid JSON request by client: %s", e)
        instance.close(4003)
