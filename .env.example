# Turn to True in Production
DEBUG=False

# Django Secret Key
# NOTES: 1. Django secret key is session unique and shouldn't be used across developers.
#        2. Generate a secret key for you from here: https://djecrety.ir/.
#        3. If your secret key is lost, your current sessions will be lost as well (cookies, passwords, etc...).
SECRET_KEY='SECRET_KEY_HERE'

# WhiskersHub Domain Setting 
# For running in development, set WHISKERS_HUB_DOMAIN='localhost'
WHISKERS_HUB_DOMAIN='SITE_DOMAIN_HERE'
CERTBOT_EMAIL= '<EMAIL>'

# Google Maps API Key
# NOTE: To obtain a key, read more here: https://developers.google.com/maps/documentation/javascript/get-api-key
GOOGLE_MAPS_API_KEY='MAPS_API_KEY_HERE'

# Postgres Settings
POSTGRES_HOST='pgbouncer'
POSTGRES_DB='whiskers'
POSTGRES_USER='postgres'
POSTGRES_PASSWORD='postgres'
POSTGRES_PORT='5432'

#PgBouncer Settings
DB_HOST='db'
DATABASE_URL="postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}"
MAX_CLIENT_CONN = '1000000'
DEFAULT_POOL_SIZE = '90'
MAX_DB_CONNECTIONS = '90'

# SMTP Credentials
# NOTES: 1. These are test credentials, replace with live credentials for production.
#        2. The test credentials use a sandbox domain, which allows testing free of charge.
#        3. To see Mailgun sandbox logs, contact <EMAIL> to be added to the sandbox users.
EMAIL_HOST='smtp.mailgun.org'
EMAIL_PORT='587'
EMAIL_HOST_USER='<EMAIL>'
EMAIL_HOST_PASSWORD='**************************************************'
EMAIL_USE_TLS='True'
EMAIL_PRIMARY_RECIPIENT='EMAIL_PRIMARY_RECIPIENT_HERE'

# Twillio Account Credentials
# NOTES: 1. These are test credentials, replace with live credentials for production.
#        2. Read more about test credentials here: https://www.twilio.com/docs/iam/test-credentials.
#        3. To see Twillio dev logs, contact <EMAIL> to gain access to the dashboard.
TWILIO_ACCOUNT_SID='**********************************'
TWILIO_AUTH_TOKEN='a3c6944b6c85fee9f23dbcd38dd0b962'
TWILIO_PHONE_NUMBER='+***********'

# API Key for local The Things Stack Webhooks
TTN_API_KEY='NNSXS.CRK4BKJ6RA3XUMXE77BV5CTUULRWWGQRIA4APWY.YTPMFSPNBJ3FKKBWOJTI7EJEQYT7AURXSQZG2JLCYARVZFO53WUA'

# VAPID Credentials
# Vapid credentials are used for Web Push Notifications
# NOTES: 1. These are test credentials, replace with live credentials for production.
VAPID_PUBLIC_KEY='VAPID_PUBLIC_KEY'
VAPID_PRIVATE_KEY='VAPID_PRIVATE_KEY'
VAPID_ADMIN_EMAIL='VAPID_ADMIN_EMAIL'

# Redis Settings
REDIS_HOST= 'redis'
REDIS_PORT= '6380'

# Python Environment Variables
LANG='C.UTF-8'
LC_ALL='C.UTF-8'
PYTHONDONTWRITEBYTECODE='1'
PYTHONUNBUFFERED='1'

# Gunicorn and Uvicorn Settings
# Optimal Workers = (2 × CPU Cores) + 1
UVICORN_WORKERS= '9'
GUNICORN_WORKERS= '9'

# Device manager > model > process_device_messages
THREADPOOL_SIZE= '20'