from functools import wraps
from django.http import HttpResponseForbidden


def local_only(view_func):
    @wraps(view_func)
    def wrapped_view(request, *args, **kwargs):
        # Check if the request's IP is in the local IP range, by checking if it starts with 172, 192 or 10
        if not str(request.META.get("REMOTE_ADDR")).startswith("172."):
            print("Access Denied")
            return HttpResponseForbidden("Access Denied")
        return view_func(request, *args, **kwargs)

    return wrapped_view
