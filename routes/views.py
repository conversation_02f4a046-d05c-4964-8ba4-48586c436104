import json
import traceback
import logging
from .utils import get_device
from django.http import HttpResponse, HttpResponseNotAllowed
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from django.core.handlers.asgi import ASGIRequest
from django.db import transaction


from device_manager.scripts import make_attr_connections
from notification_center.utils import (
    check_back_online,
    check_gateway_node,
    check_out_of_field,
    check_triggers,
    generate_global_events,
)
from device_manager.models import Device, attributes_templates
from packet_analyzer.models import DroppedPacket, UplinkPacket
from telemetry.scripts import generate_telemetries
from fields.utils import is_event_within_work_shift

logger = logging.getLogger("app")


@csrf_exempt
def ttn_app(request: ASGIRequest):
    if request.method == "POST":
        try:
            payload = json.loads(request.body.decode("utf-8"))
        except Exception as e:
            logger.error("while loading TTN packet: %s\nBody: %s",e,request.body,)
            _process_drop_packet(request.body.decode("utf-8"))
            return HttpResponse(status=400, content="Invalid JSON")

        # check if header contains API_KEY
        if "Authorization" in request.headers:
            api_key = request.headers["Authorization"]
            if api_key != settings.TTN_API_KEY:
                message="Invalid API key in request headers"
                logger.error(message)
                _process_drop_packet(payload,message)
                return HttpResponse(status=401, content=message)
        else:
            message="No API key in request headers."
            _process_drop_packet(payload,message)
            logger.error(message)
            return HttpResponse(status=401,content=message)

        try:
            if "end_device_ids" not in payload:
                message = "Device with EUI '%s' does not exist" % euid
                _process_drop_packet(payload, message)
                logger.error(message)
                return HttpResponse(status=400, content="Missing 'end_device_ids'")
            
            euid = payload["end_device_ids"]["dev_eui"]
            device= get_device(euid)
        
            if not device:
                logger.error("Device with EUI '%s' does not exist", euid)
                return HttpResponse(status=404, content="Device not found")
            if "uplink_message" in payload:
                _process_uplink(device, payload)
            return HttpResponse(status=200)
              
        except Exception as e:
            # Check if device and gateway exist
            dropped_packet=_process_drop_packet(payload)
            message = "while processing TTN packet (dropped packet id. %s): %s" % (dropped_packet.id, dropped_packet.expt)
            logger.error(message)
            return HttpResponse(status=500, content="Internal server error")

def _process_drop_packet(payload: dict,exception: str = None):
    devi = None
    gate = None
    if "end_device_ids" in payload:
        euid = payload["end_device_ids"].get("dev_eui")
        if Device.objects.filter(euid=euid).exists():
            devi = Device.objects.get(euid=euid)

    if "uplink_message" in payload:
        rx_metadata = payload["uplink_message"].get("rx_metadata", [])
        if rx_metadata:
            gateway_euid = rx_metadata[0]["gateway_ids"].get("eui")
            if Device.objects.filter(euid=gateway_euid).exists():
                gate = Device.objects.get(euid=gateway_euid)

    # Create DroppedPacket with devi and gate if they exist
    # Use traceback if exception is not provided
    exception_message = exception if exception else traceback.format_exc()

    dropped_packet = DroppedPacket.objects.create(
        devi=devi,
        gate=gate,
        data=payload,
        expt=exception_message,
        rxat=timezone.now(),
    )
    return dropped_packet
    
def _process_uplink(device: Device, payload: dict):
    # get device metadata
    uplink_pkt = payload["uplink_message"]
    rx_metadata = uplink_pkt["rx_metadata"][0]
    gateway_euid = rx_metadata["gateway_ids"]["eui"]

    gateway = get_device(gateway_euid)
    if not gateway:
        message = "Gateway with EUI '%s' does not exist" % gateway_euid
        logger.error(message)
        _process_drop_packet(payload, message)
        return HttpResponse(status=404, content=message) 
 

    # Update device attributes
    device.attr["client"].update({
        "Frame Counter": uplink_pkt.get("f_cnt", 0),
        "RSSI": rx_metadata.get("rssi", 0),
        "SNR": rx_metadata.get("snr", 0),
    })

    # Update last updated times
    device.lupd = rx_metadata["time"]
    gateway.lupd = rx_metadata["received_at"]

    
    # Check if received outside work hours
    outside_work_hours = not is_event_within_work_shift(device.fild, rx_metadata["time"])

    # Process decoded payload
    if "decoded_payload" in uplink_pkt:
        messages = uplink_pkt["decoded_payload"].get("messages", [])
        for message in messages:
            for measurement in message:
                if measurement["type"] == "Positioning Status":
                    device.attr["client"]["Positioning Status"] = measurement["measurementValue"]["statusName"]
                elif measurement["type"] == "Event Status":
                    events = [event["eventName"] for event in measurement["measurementValue"]]
                    for event in events:
                        device.attr["client"][event] = True
                    # Reset events not in the list
                    for key in list(device.attr["client"]):
                        if "event" in key and key not in events:
                            device.attr["client"][key] = False
                else:
                    device.attr["client"][measurement["type"]] = measurement["measurementValue"]

        with transaction.atomic():
            # Generate attribute connections and events
            make_attr_connections(device)

            if device.name.startswith("GWN"):
                check_gateway_node(device, gateway, outside_work_hours)
            else:
                generate_global_events(device)
                check_triggers(device, outside_work_hours)
                check_out_of_field(device)
            # Generate telemetry data
            generate_telemetries(
                device, attributes_templates[device.type]["tels"]
            )
        
            

    # Save device, gateway, and uplink packet
    with transaction.atomic():
        # Update device & gateway status
        check_back_online(device,gateway)

        device.stat = "Online"
        gateway.stat = "Online"

        Device.objects.bulk_update([device, gateway], ["attr", "lupd", "stat"])
        UplinkPacket.objects.create(
            devi=device,
            gate=gateway,
            data=uplink_pkt.get("decoded_payload", {}).get("payload", ""),
            deco=uplink_pkt,
            rssi=device.attr["client"]["RSSI"],
            snr=device.attr["client"]["SNR"],
            chan=rx_metadata.get("channel_index", 0),
            freq=uplink_pkt["settings"].get("frequency", 0.0),
            cont=device.attr["client"]["Frame Counter"],
            txat=device.lupd,
            rxat=rx_metadata["received_at"],
        )

    logger.info("'%s' sent an update.", device.name)