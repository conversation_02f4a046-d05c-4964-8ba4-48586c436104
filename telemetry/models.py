from django.db import models
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType

from device_manager.models import Device


# Create your models here.
class Telemetry(models.Model):
    devi = models.ForeignKey(Device, on_delete=models.CASCADE)
    key = models.CharField(max_length=255)
    value = models.CharField(max_length=255)
    datetime = models.DateTimeField()
