# Generated by Django 4.2.7 on 2024-05-02 15:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('device_manager', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Telemetry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.Char<PERSON>ield(max_length=255)),
                ('value', models.CharField(max_length=255)),
                ('datetime', models.DateTimeField()),
                ('devi', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device_manager.device')),
            ],
        ),
    ]
