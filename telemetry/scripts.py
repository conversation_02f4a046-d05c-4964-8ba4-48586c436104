from telemetry.models import Telemetry

telemetry_list = []


def process_sensory(entity, sensory_data):
    if "batt" in sensory_data:
        entity.batt = sensory_data["batt"] if not sensory_data["batt"] >= 100 else 100
    if "temp" in sensory_data:
        entity.temp = sensory_data["temp"]
    if "lati" in sensory_data:
        if sensory_data["lati"] != 0.0:
            entity.lati = sensory_data["lati"]
    if "long" in sensory_data:
        if sensory_data["long"] != 0.0:
            entity.long = sensory_data["long"]


def generate_telemetries(device, telemetries: list):
    # generate telemetries
    
    for tel in telemetries:
        if tel in device.attr["client"]:
            telemetry_list.append(Telemetry(
                devi=device,
                key=tel,
                value=device.attr["client"][tel],
                datetime=device.lupd,
            ))
    Telemetry.objects.bulk_create(telemetry_list)
    telemetry_list.clear()

