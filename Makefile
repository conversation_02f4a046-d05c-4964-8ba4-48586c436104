up:
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
	fi
	@if [ ! -d staticfiles ]; then \
		mkdir staticfiles; \
	fi
	@if [ ! -d media ]; then \
		mkdir media; \
	fi
	@echo "Starting WhiskersHub in Production Mode! Network server will be started."
	docker compose up --build --force-recreate db pgbouncer web redis nginx

down:
	docker compose down --remove-orphans

# ignore entrypoint.sh
migrations:
	docker compose run web python manage.py makemigrations
	docker compose run web python manage.py migrate

collectstatic:
	docker compose run web python manage.py collectstatic --noinput