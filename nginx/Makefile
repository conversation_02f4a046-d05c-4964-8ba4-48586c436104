all: build

build: Makefile Dockerfile
	docker build --progress=plain -t whiskers/nginx-certbot-http3-brotli:latest .

run:
	docker run -it --rm \
	-e CERTBOT_EMAIL=<EMAIL> \
	-e DEBUG=1 \
	whiskers/nginx-certbot-http3-brotli:latest

# These commands are primarily used for development, see link for more info:
# https://github.com/<PERSON><PERSON>lf<PERSON><PERSON>/docker-nginx-certbot/issues/28
dev:
	docker buildx build --platform linux/amd64,linux/386,linux/arm64,linux/arm/v7 --tag whiskers/nginx-certbot-http3-brotli:dev -f ./Dockerfile ./

push:
	docker buildx build --platform linux/amd64,linux/arm64 --tag whiskers/nginx-certbot-http3-brotli:dev --pull --no-cache --push .
