worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;
load_module modules/ngx_http_brotli_filter_module.so;
load_module modules/ngx_http_brotli_static_module.so;

events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    brotli on;
    brotli_static on;
    brotli_comp_level 7;
    brotli_buffers 16 8k;
    brotli_types
        text/html
        text/plain
        text/css
        text/javascript
        application/javascript
        application/json
        application/xml
        application/xml+rss
        font/ttf
        font/otf
        font/woff
        font/woff2
        image/svg+xml;

    gzip on;
    gzip_static on;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_proxied any;
    gzip_types
        text/html
        text/plain
        text/css
        text/javascript
        text/xml
        application/javascript
        application/x-javascript
        application/json
        application/xml
        application/xml+rss
        image/svg+xml;
    gzip_vary on;

    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/user.conf.d/*.conf;
}