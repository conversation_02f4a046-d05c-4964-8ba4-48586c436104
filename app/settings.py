"""
Django settings for app project.

Generated by 'django-admin startproject' using Django 4.1.7.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""

from pathlib import Path
import os
import sys
import logging
from decouple import config

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config("SECRET_KEY", cast=str)

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config("DEBUG", default=False, cast=bool)

ALLOWED_HOSTS = [
    "*",
]

LOGIN_URL = "accounts:login"

MEDIA_URL = "/media/"
MEDIA_ROOT = os.path.join(BASE_DIR, "media")

# Application definition

INSTALLED_APPS = [
    "daphne",
    "channels",
    "device_manager.apps.DeviceManagerConfig",
    "network_monitor.apps.NetworkMonitorConfig",
    "packet_analyzer.apps.PacketAnalyzerConfig",
    "telemetry.apps.TelemetryConfig",
    "notification_center.apps.NotificationsCenterConfig",
    "fields.apps.FieldsConfig",
    "settings.apps.SettingsConfig",
    "accounts.apps.AccountsConfig",
    "configuration.apps.ConfigurationConfig",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django_extensions",
    "webpush",
    "pwa",
]

MIDDLEWARE = [
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "app.middleware.RequestLoggingMiddleware",
]

ROOT_URLCONF = "app.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [str(BASE_DIR / "templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

ASGI_APPLICATION = "app.asgi.application"
WSGI_APPLICATION = "app.wsgi.application"

REDIS_HOST=config("REDIS_HOST", cast=str)
REDIS_PORT=config("REDIS_PORT", cast=int)
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [
                (
                    REDIS_HOST,
                    REDIS_PORT,
                )
            ],
        },
    },
}

# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": config("POSTGRES_DB", cast=str),
        "USER": config("POSTGRES_USER", cast=str),
        "PASSWORD": config("POSTGRES_PASSWORD", cast=str),
        "HOST": config("POSTGRES_HOST", cast=str, default="localhost"),
        "PORT": config("POSTGRES_PORT", cast=int, default=5432),
        "CONN_HEALTH_CHECKS": True,
    }
}

WEBPUSH_SETTINGS = {
    "VAPID_PUBLIC_KEY": config("VAPID_PUBLIC_KEY", cast=str),
    "VAPID_PRIVATE_KEY": config("VAPID_PRIVATE_KEY", cast=str),
    "VAPID_ADMIN_EMAIL": config("VAPID_ADMIN_EMAIL", cast=str),
}

class CustomFormatter(logging.Formatter):
    def format(self, record):
        # Extract the app name from the pathname
        record.pathname = os.path.basename(os.path.dirname(record.pathname))
        return super().format(record)


log_dir = "logs"

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "simple": {
            "format": "{asctime} {levelname} {module} {message}",
            "style": "{",
            "datefmt": "[%d/%b/%Y %H:%M:%S]",
        },
        "verbose": {
            "()": CustomFormatter,
            "format": "{asctime} {levelname} {pathname}/{module}:{lineno} {message}",
            "style": "{",
            "datefmt": "[%d/%b/%Y %H:%M:%S]",
        },
    },
    "handlers": {
        "file_djn": {
            "level": "INFO",
            "class": "logging.FileHandler",
            "filename": os.path.join(log_dir, "djn.log"),
            "formatter": "simple",
        },
        "file_app": {
            "level": "DEBUG",
            "class": "logging.FileHandler",
            "filename": os.path.join(log_dir, "app.log"),
            "formatter": "verbose",
        },
    },
    "loggers": {
        "django": {
            "handlers": ["file_djn"],
            "level": "INFO",
            "propagate": True,
        },
        "app": {
            "handlers": ["file_app"],
            "level": "DEBUG",
            "propagate": True,
        },
    },
}


WHISKERS_HUB_DOMAIN = config("WHISKERS_HUB_DOMAIN")

# Email
EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = config("EMAIL_HOST")
EMAIL_PRIMARY_RECIPIENT = config("EMAIL_PRIMARY_RECIPIENT")

# or the appropriate port for your SMTP server
EMAIL_PORT = config("EMAIL_PORT", cast=int)
# or False if not using TLS/SSL
EMAIL_USE_TLS = config("EMAIL_USE_TLS", cast=bool)
EMAIL_HOST_USER = config("EMAIL_HOST_USER")
EMAIL_HOST_PASSWORD = config("EMAIL_HOST_PASSWORD")

# Twilio
TWILIO_ACCOUNT_SID = config("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = config("TWILIO_AUTH_TOKEN")
TWILIO_PHONE_NUMBER = config("TWILIO_PHONE_NUMBER")

# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = "en-us"

# muscat
TIME_ZONE = "Asia/Muscat"

USE_I18N = True

USE_TZ = False


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = "/static/"
STATICFILES_DIRS = (str(BASE_DIR.joinpath("static")),)
STATIC_ROOT = str(BASE_DIR.joinpath("staticfiles"))
STATICFILES_FINDERS = [
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
]

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

GOOGLE_MAPS_API_KEY = os.getenv("GOOGLE_MAPS_API_KEY")
TTN_API_KEY = config("TTN_API_KEY", cast=str)

CSRF_TRUSTED_ORIGINS = ["https://" + config("WHISKERS_HUB_DOMAIN")]

USE_X_FORWARDED_HOST = True
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

# PWA App
PWA_APP_NAME = "WhiskersHub"
PWA_APP_DESCRIPTION = "A fully integrated IoT platform"
PWA_APP_THEME_COLOR = "#2e74d6"
PWA_APP_BACKGROUND_COLOR = "#ffffff"
PWA_APP_DISPLAY = "standalone"
PWA_APP_SCOPE = "/"
PWA_APP_ORIENTATION = "any"
PWA_APP_START_URL = "/"
PWA_APP_STATUS_BAR_COLOR = "#313a46"
PWA_APP_ICONS = [{"src": "/static/images/icons/play_store_512.png", "sizes": "512x512"}]
PWA_APP_ICONS_APPLE = [
    {"src": "/static/images/icons/play_store_512.png", "sizes": "512x512"}
]
PWA_APP_DIR = "ltr"
PWA_APP_LANG = "en-US"
PWA_APP_SHORTCUTS = [
    {
        "name": "Device Manager",
        "url": "/device/list",
        "description": "View registered devices",
    },
    {
        "name": "Notification Center",
        "url": "/notification_center/list",
        "description": "View and manage history of notifications",
    },
]
PWA_APP_SCREENSHOTS = [
    {
        "src": "/static/images/mobile-screenshots/1.jpg",
        "sizes": "1080x2340",
        "type": "image/jpg",
    },
    {
        "src": "/static/images/mobile-screenshots/2.jpg",
        "sizes": "1080x2340",
        "type": "image/jpg",
    },
    {
        "src": "/static/images/mobile-screenshots/3.jpg",
        "sizes": "1080x2340",
        "type": "image/jpg",
    },
]
