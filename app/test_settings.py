"""
Django test settings for app project.
This file contains settings specifically for running tests.
"""

from .settings import *

# Use direct connection to postgressql for testing
# This is because pgbouncer blocking test database creation in transaction pooling mode(production mode)
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": config("POSTGRES_DB", cast=str),
        "USER": config("POSTGRES_USER", cast=str),
        "PASSWORD": config("POSTGRES_PASSWORD", cast=str),
        "HOST": config("DB_HOST", cast=str, default="db"),
        "PORT": config("POSTGRES_PORT", cast=int, default=5432),
        "CONN_HEALTH_CHECKS": True,
    }
}

# Disable any external services during testing
EMAIL_BACKEND = 'django.core.mail.backends.dummy.EmailBackend'