################################ Build ################################
FROM python:3.11.4-slim AS build

# Pipenv recommendation: https://github.com/pypa/pipenv/blob/main/docs/docker.md
ENV PIPENV_VENV_IN_PROJECT=1

WORKDIR /usr/src

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends libgdal-dev postgresql-client && \
    apt-get clean && \
    apt-get autoremove && \
    rm -rf /var/lib/apt/lists/*

# Install pipenv
RUN pip install --upgrade pip && \
    pip install pipenv

# Copy Pipfile
COPY Pipfile Pipfile.lock ./

# Install python dependencies
RUN pipenv install --deploy --ignore-pipfile

FROM edoburu/pgbouncer:v1.24.0-p1
#Ensure the /etc/pgbouncer directory has the correct permissions
RUN mkdir -p /etc/pgbouncer && \
    chown -R postgres:postgres /etc/pgbouncer



################################ Production ################################
FROM build

ENV APP_HOME="/home/<USER>"
# Add non-root user
RUN adduser --disabled-password --gecos '' app

# Set work directory and environment variables

RUN mkdir -p $APP_HOME && chown app:app $APP_HOME

WORKDIR $APP_HOME

# Update PATH to include the virtual environment's bin directory
ENV PATH="/usr/src/.venv/bin:$PATH"

# Copy python dependencies from build stage
COPY --from=build --chown=app:app /usr/src/.venv /usr/src/.venv

# Copy source code
COPY --chown=app:app . .

# Make entrypoint.sh executable
RUN  ./entrypoint.sh && \
    chmod +x entrypoint.sh

# Run entrypoint.sh
USER app
ENTRYPOINT ["sh", "/home/<USER>/entrypoint.sh"]
